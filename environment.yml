name: cdc
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py310h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.7.2=h06a4308_0
  - certifi=2024.7.4=py310h06a4308_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.6.37=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.6=3
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py310heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - idna=3.7=py310h06a4308_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.4=py310h06a4308_0
  - joblib=1.4.2=py310h06a4308_0
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=*********=0
  - libcurand=*********=0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.17=h5eee18b_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgfortran-ng=11.2.0=h00389a5_1
  - libgfortran5=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp-base=1.3.2=h5eee18b_0
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py310h5eee18b_1
  - mkl_fft=1.3.8=py310h5eee18b_0
  - mkl_random=1.2.4=py310hdb19cb5_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.3=py310h06a4308_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.0.14=h5eee18b_0
  - pillow=10.4.0=py310h5eee18b_0
  - pip=24.0=py310h06a4308_0
  - pybind11-abi=4=hd3eb1b0_1
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.14=h955ad1f_1
  - pytorch=2.3.1=py3.10_cuda12.1_cudnn8.9.2_0
  - pytorch-cuda=12.1=ha16c6d3_5
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.1=py310h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.3=py310h06a4308_0
  - scikit-learn=1.5.1=py310h1128e8f_0
  - scipy=1.13.1=py310h5f9d8c6_0
  - setuptools=69.5.1=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - sympy=1.12=py310h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - threadpoolctl=3.5.0=py310h2f386ee_0
  - tk=8.6.14=h39e8969_0
  - torchaudio=2.3.1=py310_cu121
  - torchtriton=2.3.1=py310
  - torchvision=0.18.1=py310_cu121
  - typing_extensions=4.11.0=py310h06a4308_0
  - urllib3=2.2.2=py310h06a4308_0
  - wheel=0.43.0=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.5=hc292b87_2
  - pip:
      - accelerate==0.33.0
      - altair==5.3.0
      - annotated-types==0.7.0
      - anyio==4.4.0
      - attrs==23.2.0
      - av==12.3.0
      - bitsandbytes==0.43.3
      - blinker==1.8.2
      - cachetools==5.4.0
      - click==8.1.7
      - distro==1.9.0
      - einops==0.8.0
      - exceptiongroup==1.2.2
      - faiss-gpu==1.7.2
      - filelock==3.15.4
      - flash-attn==2.6.3
      - fsspec==2024.6.1
      - gitdb==4.0.11
      - gitpython==3.1.43
      - h11==0.14.0
      - httpcore==1.0.5
      - httpx==0.27.0 
      - huggingface-hub==0.24.6
      - jsonschema==4.23.0
      - jsonschema-specifications==2023.12.1
      - markdown-it-py==3.0.0
      - markupsafe==2.1.5
      - mdurl==0.1.2
      - numpy==1.23.3
      - ollama==0.3.1
      - openai==1.35.10
      - opencv-python==********
      - packaging==24.1
      - pandas==2.2.2
      - protobuf==5.27.3
      - psutil==6.0.0
      - pyarrow==17.0.0
      - pydantic==2.8.2
      - pydantic-core==2.20.1
      - pydeck==0.9.1
      - pygments==2.18.0
      - python-dateutil==2.9.0.post0
      - pytz==2024.1
      - referencing==0.35.1
      - regex==2024.7.24
      - rich==13.7.1
      - rpds-py==0.19.1
      - safetensors==0.4.4
      - six==1.16.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - streamlit==1.37.0
      - tenacity==8.5.0
      - tokenizers==0.19.1
      - toml==0.10.2
      - toolz==0.12.1
      - tornado==6.4.1
      - tqdm==4.66.4
      - transformers==4.44.1
      - typing-extensions==4.12.2
      - tzdata==2024.1
      - watchdog==4.0.1
