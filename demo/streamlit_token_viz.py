import streamlit as st
import pandas as pd
import altair as alt
import os

st.title("GPT-4o Vision Use-Case Cost & Annotation Dashboard")

# Backend CSV paths (update these paths as needed)
ANNOTATIONS_CSV_PATH = "/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/assets/annotations.csv"
USECASE_CSV_PATH = "/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/assets/new_usecase_with_paths.csv"

@st.cache_data
def load_csv(path):
    return pd.read_csv(path)

# Load CSV files
df_annotations = load_csv(ANNOTATIONS_CSV_PATH)
df_usecase = load_csv(USECASE_CSV_PATH)

# Ensure "Clip time" is treated as a string
df_usecase["Clip time"] = df_usecase["Clip time"].astype(str)

st.subheader("Annotations CSV Preview")
st.dataframe(df_annotations.head())
st.subheader("New Use-Case CSV Preview")
st.dataframe(df_usecase.head())

st.markdown("---")
st.subheader("Compute Average Annotation Tokens & Cost from Annotations CSV")

st.markdown("""
Each annotation in the annotations CSV comes from a sliding-window analysis of a clip.
The measured **input_tokens** represent the text prompt.
Since 3 images are passed with each prompt, we add a fixed vision cost per image:
- **Low detail:** each image costs **85 tokens**
- **High detail:** a 512×512 image in high-res mode costs **255 tokens** (170 + 85)

Pricing:
- **Text Input:** \$5 per 1M tokens  
- **Text Output:** \$15 per 1M tokens  
- **Vision Processing:** \$2.50 per 1M tokens
(as described in [this Analytics Vidhya blog](https://www.analyticsvidhya.com/blog/2024/12/openai-api-cost/))
""")

detail_mode = st.selectbox("Select Image Detail Mode", ["low", "high"])
if detail_mode == "low":
    image_token_cost = 85
else:
    image_token_cost = 255

st.write(f"Using {detail_mode} detail mode: each image costs **{image_token_cost} tokens**.")

# Adjust input tokens: original text prompt tokens + (3 images * image cost)
df_annotations["adjusted_input_tokens"] = df_annotations["input_tokens"] + 3 * image_token_cost

# Calculate overall token sums and averages
total_text_input_tokens = df_annotations["input_tokens"].sum()
total_vision_tokens = df_annotations.shape[0] * 3 * image_token_cost  # three images per annotation
total_output_tokens = df_annotations["output_tokens"].sum()

avg_text_input = df_annotations["input_tokens"].mean()
avg_adjusted_input = df_annotations["adjusted_input_tokens"].mean()
avg_output = df_annotations["output_tokens"].mean()

st.write(f"Average Text Input Tokens per Annotation: **{avg_text_input:.1f}**")
st.write(f"Average Adjusted Input Tokens (text + images) per Annotation: **{avg_adjusted_input:.1f}**")
st.write(f"Average Output Tokens per Annotation: **{avg_output:.1f}**")

# Compute costs using blog pricing:
# Text Input: $5 / 1,000,000 tokens, Vision: $2.50 / 1,000,000 tokens, Text Output: $15 / 1,000,000 tokens.
cost_text_input = (total_text_input_tokens / 1e6) * 5
cost_vision = (total_vision_tokens / 1e6) * 2.50
cost_text_output = (total_output_tokens / 1e6) * 15
total_annotations_cost = cost_text_input + cost_vision + cost_text_output

st.markdown("**Annotations CSV Cost Breakdown:**")
st.write(f"Total Text Input Tokens: **{total_text_input_tokens:.0f}** costing **${cost_text_input:.4f}**")
st.write(f"Total Vision Tokens (for images): **{total_vision_tokens:.0f}** costing **${cost_vision:.4f}**")
st.write(f"Total Text Output Tokens: **{total_output_tokens:.0f}** costing **${cost_text_output:.4f}**")
st.write(f"**Total Estimated Cost for Annotations CSV: ${total_annotations_cost:.4f}**")

st.markdown("---")
st.subheader("Estimate Tokens & Cost for New Use-Case Clips")

st.markdown("""
For each clip in the new use-case CSV, we parse the "Clip time" (e.g., "0:20-0:28") to determine its duration in seconds.
We assume a sliding-window annotation is generated every second between (start + 1) and (end - 1), i.e.:
**Number of annotations per clip = max(0, (end_sec - start_sec) - 1)**
Using the average tokens from the annotations CSV, we then estimate the total tokens for each clip.
""")

def parse_time_str(time_str):
    parts = time_str.strip().split(":")
    return int(parts[0]) * 60 + int(parts[1])

def annotations_per_clip(clip_time):
    try:
        start_str, end_str = clip_time.split("-")
        start_sec = parse_time_str(start_str)
        end_sec = parse_time_str(end_str)
        return max(0, (end_sec - start_sec) - 1)
    except Exception:
        return 0

df_usecase["annotation_count"] = df_usecase["Clip time"].apply(annotations_per_clip)
df_usecase["est_text_input_tokens"] = df_usecase["annotation_count"] * avg_text_input
df_usecase["est_vision_tokens"] = df_usecase["annotation_count"] * 3 * image_token_cost
df_usecase["est_output_tokens"] = df_usecase["annotation_count"] * avg_output
df_usecase["est_total_input_tokens"] = df_usecase["est_text_input_tokens"] + df_usecase["est_vision_tokens"]

total_est_text_input = df_usecase["est_text_input_tokens"].sum()
total_est_vision = df_usecase["est_vision_tokens"].sum()
total_est_output = df_usecase["est_output_tokens"].sum()

est_cost_text_input = (total_est_text_input / 1e6) * 5
est_cost_vision = (total_est_vision / 1e6) * 2.50
est_cost_text_output = (total_est_output / 1e6) * 15
total_usecase_cost = est_cost_text_input + est_cost_vision + est_cost_text_output

st.write("**Estimated Tokens per Clip (first few rows):**")
st.dataframe(df_usecase[["Video name", "Clip time", "annotation_count", 
                           "est_text_input_tokens", "est_vision_tokens", "est_output_tokens"]].head())

st.markdown("**Estimated Cost for New Use-Case Dataset:**")
st.write(f"Total Estimated Text Input Tokens: **{total_est_text_input:.0f}**")
st.write(f"Total Estimated Vision Tokens: **{total_est_vision:.0f}**")
st.write(f"Total Estimated Output Tokens: **{total_est_output:.0f}**")
st.write(f"Cost for Text Input Tokens (@ $5/1M): **${est_cost_text_input:.4f}**")
st.write(f"Cost for Vision Tokens (@ $2.50/1M): **${est_cost_vision:.4f}**")
st.write(f"Cost for Text Output Tokens (@ $15/1M): **${est_cost_text_output:.4f}**")
st.write(f"**Total Estimated Cost for Use-Case Dataset: ${total_usecase_cost:.4f}**")

st.markdown("---")
st.subheader("Visualize Output Keywords by Class")

# Derive a class from video_name (e.g., "blocked_pathway_4" -> "blocked_pathway")
df_annotations["class"] = df_annotations["video_name"].apply(lambda x: x.rsplit("_", 1)[0] if "_" in x else x)

# Flatten keywords from the annotation text for visualization
keyword_list = []
for _, row in df_annotations.iterrows():
    for kw in row["annotation"].replace('"', '').split(","):
        kw = kw.strip()
        if kw:
            keyword_list.append({"class": row["class"], "keyword": kw})
df_kw = pd.DataFrame(keyword_list)

st.write("**Keyword Frequency by Class**")
chart = alt.Chart(df_kw).mark_bar().encode(
    x=alt.X("keyword:N", title="Keyword", sort="-y"),
    y=alt.Y("count():Q", title="Count"),
    color="class:N",
    tooltip=["keyword", alt.Tooltip("count():Q", title="Count"), "class"]
).properties(width=700, height=400)
st.altair_chart(chart)
