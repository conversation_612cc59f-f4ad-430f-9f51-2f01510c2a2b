import streamlit as st
import requests
import numpy as np
import time
import json
import re
import ast
import base64

st.set_page_config(layout="wide", page_title="Surveillance Dashboard")

# Custom CSS to improve the look
st.markdown("""
    <style>
    .reportview-container {
        background-color: #f0f2f6;
    }
    .big-font {
        font-size:20px !important;
        font-weight: bold;
    }
    .stAlert {
        background-color: #ff4b4b;
        color: white;
        padding: 10px;
        border-radius: 5px;
    }
    .stInfo {
        background-color: #0068c9;
        color: white;
        padding: 10px;
        border-radius: 5px;
    }
    .center-title {
        text-align: center;
        margin-top: -70px;
    }
    .vertical-bar {
        border-left: 2px solid #e0e0e0;
        height: 100vh;
        position: absolute;
        top: 0;
    }
    .logo-img {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 200px;
    }
    .frame-container {
        display: flex;
        justify-content: center;
    }
    .frame-container img {
        max-width: 75%;
        height: auto;
    }
    </style>
    """, unsafe_allow_html=True)

# Helper functions remain the same
def clean_json_string(json_string):
    json_string = json_string.strip()
    json_string = re.sub(r'^```json\n|```$', '', json_string, flags=re.MULTILINE)
    json_string = json_string.replace("'", '"')
    return json_string


def parse_json_safely(json_string):
    try:
        return json.loads(clean_json_string(json_string))
    except json.JSONDecodeError:
        try:
            return ast.literal_eval(json_string)
        except:
            st.error(f"Failed to parse data: {json_string}")
            return None


def display_analysis(analysis):
    if analysis:
        st.markdown("### Analysis Results")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**Suspicious:** {analysis.get('is_suspicious', 'N/A')}")
            st.markdown(f"**Activity:** {analysis.get('suspicious_activity', 'N/A')}")
        with col2:
            st.markdown(f"**Suspicion Level:** {analysis.get('level_of_suspicion', 'N/A')}")
        st.markdown(f"**Reasoning:** {analysis.get('reasoning', 'N/A')}")


def main():
    # Add logo to the top right corner
    with open("data/assets/logo_white_bg.png", "rb") as f:
        contents = f.read()
        data_url = base64.b64encode(contents).decode("utf-8")
    st.markdown(
        f'<img src="data:image/png;base64,{data_url}" class="logo-img">',
        unsafe_allow_html=True
    )

    # Centered title
    st.markdown("<h1 class='center-title'>🎥 Live Surveillance Dashboard</h1>", unsafe_allow_html=True)

    # Create three columns: alerts, frame, and analysis
    col1, col2, col3 = st.columns([1, 2, 1])

    with col1:
        st.markdown("### 🚨 Alerts")
        alert_placeholder = st.empty()
        alert_placeholder.info("No alerts at the moment.")

    with col2:
        st.markdown("### 📹 Live Feed")
        frame_placeholder = st.empty()

    with col3:
        st.markdown("### 🔍 Analysis")
        analysis_placeholder = st.empty()

    # Add vertical bars
    # st.markdown('<div class="vertical-bar" style="left: 33.33%;"></div>', unsafe_allow_html=True)
    # st.markdown('<div class="vertical-bar" style="left: 66.66%;"></div>', unsafe_allow_html=True)

    while True:
        try:
            response = requests.get('http://localhost:8000')
            data = response.json()

            if 'frame' in data and data['frame'] is not None:
                frame = np.array(data['frame'], dtype=np.uint8)
                frame_placeholder.image(frame, channels="BGR", use_column_width=True)

            if 'analysis' in data and data['analysis']:
                analysis = parse_json_safely(data['analysis'])
                if analysis:
                    with analysis_placeholder.container():
                        display_analysis(analysis)

                    # Update alert if suspicious
                    if analysis.get('is_suspicious', 'No').lower() == 'yes':
                        alert_placeholder.warning(
                            f"🚨 Alert: {analysis.get('suspicious_activity', 'Suspicious activity detected')}")
                    else:
                        alert_placeholder.info("No alerts at the moment.")

            time.sleep(0.1)  # Adjust as needed to control refresh rate

        except requests.exceptions.RequestException:
            st.error("Unable to connect to the pipeline server. Is it running?")
            time.sleep(1)
        except Exception as e:
            st.error(f"An error occurred: {str(e)}")
            time.sleep(1)


if __name__ == "__main__":
    main()