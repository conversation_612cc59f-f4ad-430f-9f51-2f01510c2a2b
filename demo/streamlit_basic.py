import streamlit as st
import json
import cv2
from PIL import Image
import io
import base64

# Function to load alerts from the JSON file
def load_alerts(json_path):
    try:
        with open(json_path, 'r') as f:
            alerts = json.load(f)
    except Exception as e:
        st.error(f"Error loading alerts: {e}")
        alerts = []
    return alerts

# Function to display alert details
def display_alert(alert):
    st.video(alert['input'])
    st.write(f"**Alert Type:** {alert['output']['suspicious_activity']}")
    st.write(f"**Level of Suspicion:** {alert['output']['level_of_suspicion']}")
    st.write(f"**Reasoning:** {alert['output']['reasoning']}")

# Function to generate thumbnail from video path
def get_video_thumbnail(video_path):
    cap = cv2.VideoCapture(video_path)
    success, frame = cap.read()
    if success:
        _, buffer = cv2.imencode('.jpg', frame)
        thumbnail = base64.b64encode(buffer).decode('utf-8')
        cap.release()
        return thumbnail
    cap.release()
    return None

# Main function
def main():
    st.sidebar.image("data/assets/logo_white_bg.png", use_column_width=True)
    st.sidebar.markdown("<br>", unsafe_allow_html=True)
    st.sidebar.markdown("<br>", unsafe_allow_html=True)
    st.title("Surveillance Monitoring Dashboard")

    # Load alerts
    alerts = load_alerts('data/processed/alerts.json')

    if not alerts:
        st.warning("No alerts to display")
        return

    # Sidebar for alert notifications
    st.sidebar.title("Alert Notifications")

    # Initialize session state for selected alert
    if "selected_alert_index" not in st.session_state:
        st.session_state.selected_alert_index = -1

    # Iterate through alerts and display in sidebar
    for i, alert in enumerate(alerts):
        if alert['output']['is_suspicious'].lower() in ['yes', '1']:
            thumbnail = get_video_thumbnail(alert['input'])
            if thumbnail:
                col1, col2 = st.sidebar.columns([1.5, 2])
                with col1:
                    st.image(f"data:image/jpeg;base64,{thumbnail}", use_column_width=True)
                with col2:
                    st.write(f"**{alert['output']['suspicious_activity']}**")
                    st.write(f"Level of Suspicion: {alert['output']['level_of_suspicion']}")
                    if st.button(f"Select Alert {i+1}", key=f"alert_button_{i}"):
                        st.session_state.selected_alert_index = i
                st.sidebar.markdown("---")  # Adds a horizontal line for separation

    # Display selected alert in the main area
    selected_alert_index = st.session_state.selected_alert_index
    if selected_alert_index != -1:
        selected_alert = alerts[int(selected_alert_index)]
        display_alert(selected_alert)

if __name__ == "__main__":
    main()
