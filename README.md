LLuminaAI -
 # ReadMe

 ## ToDo's
- [x] Embedding/Vectorization: Nomic/mxbai embedding instead of OAI embedding
- [x] Logging: Add logging + Grafana for modules
- [x] Vector Store: Add Milvus/Qdrant/ChromaDB support 
- [x] Vector Store: Graph DB + Eval
- [x] Prompt: Use context along with prompt
- [x] Prompt: Prompt tuning

 ## Done's
 
 ### Setup Paths
 ```
 $env:PYTHONPATH="C:\Users\<USER>\Desktop\projects\vlm_constructor_deconstructor"
 ```
### How to run?
```

##set the path 
Navigate project_directory

export PYTHONPATH=$(pwd)

## for video directory
python scripts/main.py --pipeline video --video_folder /SOLUTION/standard_dataset/burglary/  --eval_dir data/eval/hugging_face/

##for single video
python scripts/main.py --pipeline video --video_path /SOLUTION/standard_dataset/burglary/burglary_1.mp4  --eval_dir data/eval/hugging_face/

 ```
##for stream usage 

python scripts/main.py --pipeline stream --stream_url  rtsp:/**************:8554/stream11


