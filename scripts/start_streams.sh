#!/bin/bash
# This script creates a tmux session with multiple windows, each running a different ffmpeg command.

# Check for required dependencies
command -v tmux >/dev/null 2>&1 || { echo "tmux is required but not installed. Aborting." >&2; exit 1; }
command -v ffmpeg >/dev/null 2>&1 || { echo "ffmpeg is required but not installed. Aborting." >&2; exit 1; }

# Check if the IP address is provided as an argument
if [ -z "$1" ]; then
  echo "Usage: $0 <IP_ADDRESS>"
  exit 1
fi

# Assign the provided IP address to a variable
IP_ADDRESS=$1

# Check if tmux session already exists
tmux has-session -t my_session 2>/dev/null
if [ $? -eq 0 ]; then
    echo "Session 'my_session' already exists. Killing it..."
    tmux kill-session -t my_session
fi

# Create a new tmux session
tmux new-session -d -s my_session

# Define the ffmpeg commands with different inputs
ffmpeg_commands=(
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/ces_demo/normal_vidoes/4.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream1"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/ces_demo/normal_vidoes/21.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream2"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/DEmo_DATE_April_22/Stream_3.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream3"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/GTC_DEMO/Normal_videos_downloads/Normal_video_4.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream4"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/demo/indoor/grid_1retail/stream_2/9.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream5"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/GTC_DEMO/Normal_videos_downloads/Normal_video_6.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream6"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/DEmo_DATE_April_22/Stream7.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream7"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/ces_demo/normal_vidoes/14.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream8"
  "ffmpeg -stream_loop -1 -re -i /SOLUTION/DEmo_DATE_April_22/STream9.mp4 -c:v copy -rtsp_transport tcp -f rtsp rtsp://$IP_ADDRESS:8554/stream9"
)

# Check if video files exist
for cmd in "${ffmpeg_commands[@]}"; do
    video_path=$(echo "$cmd" | grep -o '/SOLUTION/.*\.mp4')
    if [ ! -f "$video_path" ]; then
        echo "Error: Video file not found: $video_path"
        exit 1
    fi
done

# Run each ffmpeg command in a new tmux window
for i in "${!ffmpeg_commands[@]}"; do
    tmux new-window -t my_session:$((i+1)) -n "ffmpeg_$((i+1))" "${ffmpeg_commands[$i]}"
done

# Start the alert script in a new window with proper environment
tmux new-window -t my_session:$((${#ffmpeg_commands[@]}+1)) -n "alerts" "cd /home/<USER>/rmidigudla/deconstructur_lean/lluminaai && PYTHONPATH=/home/<USER>/rmidigudla/deconstructur_lean/lluminaai python3 scripts/demo_alerts.py"

# Select the alerts window to show its output
tmux select-window -t my_session:$((${#ffmpeg_commands[@]}+1))

# Attach to the tmux session
tmux attach-session -t my_session
