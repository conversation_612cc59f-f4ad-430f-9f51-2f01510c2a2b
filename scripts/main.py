# import sys
# import re
# import os
# import json
# import argparse
# import numpy as np
# from src.pipelines import pipeline_image_basic, pipeline_video_basic, pipeline_video_stream_lean, pipeline_video_basic_lean
# from src.utils.yaml_utils import load_yaml


# #passing the arguments to the pipeline script to use run_pipeline function
# def process_video(video_path, config_path, actions_path, prompts_path, context_path, eval_dir, output_path, debug):
#     """Process a video using the configured pipeline."""
#     config = load_yaml(config_path)
#     try:
#         if config.get('pipeline', {}).get('use_lean_deconstructor', False):
#             print(f"Using lean deconstructor for video: {video_path}")
#             pipeline_video_basic_lean.run_pipeline(
#                 config_path=config_path,
#                 video_path=video_path,
#                 actions_path=actions_path,
#                 prompts_path=prompts_path,
#                 context_path=context_path,
#                 debug=debug,
#                 chunk_duration=15,
#                 output_path=output_path
#             )
#         else:
#             print(f"Using basic pipeline for video: {video_path}")
#             pipeline_video_basic.run_pipeline(
#                 config_path=config_path,
#                 video_path=video_path,
#                 actions_path=actions_path,
#                 prompts_path=prompts_path,
#                 context_path=context_path,
#                 debug=debug,
#                 chunk_duration=15,
#                 output_path=output_path
#             )

#     except Exception as e:
#         print(f"Error processing video {video_path}: {e}")
    
#     def process_stream(stream_url, config_path, actions_path, prompts_path, context_path, eval_dir, output_path, debug):




# def main():
#     parser = argparse.ArgumentParser(description='Run different VLM pipelines.')
#     parser.add_argument('-p', '--pipeline', choices=['image', 'video','stream'], default='video',
#                         help='Select the pipeline to run: image or video or Stream(default: video).')
#     parser.add_argument('-i', '--image_path', type=str, default=None,
#                         help='Path to the image file (required for image pipeline).')
#     parser.add_argument('-v', '--video_path', type=str, default=None,
#                         help='Path to the video file (required for video pipeline).')
#     parser.add_argument('-s', '--stream_url', type=str, default=None,
#                         help='RTSP stream url link.')
#     parser.add_argument('-f', '--video_folder', type=str, default=None,
#                         help='Path to a folder containing video files.')
#     parser.add_argument('-c', '--config_path', type=str, default='config/model/default.yaml',
#                         help='Path to the config file.')
#     parser.add_argument('-o', '--output_path', type=str, default='/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/processed/office/qween_2b/stream_alerts.json',
#                         help='Path to the output alerts JSON file.')
#     parser.add_argument('-e', '--eval_dir', type=str, default='data/eval/hugging_face',
#                         help='Directory to store evaluation data.')
#     parser.add_argument('-d', '--debug', action='store_true', help='Sets debug mode.')

#     args = parser.parse_args()


#     #Load configuration file
#     with open(args.config_path, 'r') as config_file:
#         config = load_yaml(config_file)

#     #Extract paths from config
#     actions_path = config.get('app', {}).get('actions', 'config/prompt/actions.json')
#     prompts_path = config.get('app', {}).get('prompts', 'config/prompt/prompts.json')    
#     context_path = config.get('app', {}).get('context', 'config/prompt/context.json') 


#     if args.pipeline == 'video':
#         if args.video_folder:
#             # Process all video files in the folder
#             for file_name in os.listdir(args.video_folder):
#                 if file_name.endswith(('.mp4', '.avi', '.mkv')):  # Add other extensions if needed
#                     video_path = os.path.join(args.video_folder, file_name)
#                     print(f"Processing video: {video_path}")
#                     process_video(video_path, args.config_path, actions_path, prompts_path,
#                                   context_path, args.eval_dir, args.output_path, args.debug)
#         elif args.video_path:
#             # Process a single video file
#             print(f"Processing video: {args.video_path}")
#             process_video(args.video_path, args.config_path, actions_path, prompts_path,
#                           context_path, args.eval_dir, args.output_path, args.debug)
            
#         elif args.stream_url:
#             # Process a single video stream
#             print(f"Processing video: {args.stream_url}")
#             process_video(args.stream_url, args.config_path, actions_path, prompts_path,
#                           context_path, args.eval_dir, args.output_path, args.debug)
#         else:
#             print("Error: --video_path or --video_folder is required for the video pipeline.")
#             sys.exit(1)
            
#     elif args.pipeline == 'image':
#         if not args.image_path:
#             print("Error: --image_path is required for the image pipeline.")
#             sys.exit(1)
#         analysis = pipeline_image_basic.run_pipeline(args.config_path, args.image_path, actions_path, prompts_path,
#                                                      context_path, args.debug)
#         print("Image analysis completed.")
#     else:
#         print("Invalid pipeline choice. Use --help for options.")
#         sys.exit(1)


# if __name__ == "__main__":
#     main()
import sys
import re
import os
import json
import argparse
import numpy as np
from src.pipelines import pipeline_image_basic, pipeline_video_basic, pipeline_video_stream_lean, pipeline_video_basic_lean
from src.utils.yaml_utils import load_yaml

# Passing the arguments to the pipeline script to use run_pipeline function
def process_video(video_path, config_path, actions_path, prompts_path, context_path, eval_dir, output_path, debug):
    """Process a video using the configured pipeline."""
    config = load_yaml(config_path)
    try:
        if config.get('pipeline', {}).get('use_lean_deconstructor', False):
            print(f"Using lean deconstructor for video: {video_path}")
            pipeline_video_basic_lean.run_pipeline(
                config_path=config_path,
                video_path=video_path,
                actions_path=actions_path,
                prompts_path=prompts_path,
                context_path=context_path,
                debug=debug,
                chunk_duration=20,
                output_path=output_path
            )
        else:
            print(f"Using basic pipeline for video: {video_path}")
            pipeline_video_basic.run_pipeline(
                config_path=config_path,
                video_path=video_path,
                actions_path=actions_path,
                prompts_path=prompts_path,
                context_path=context_path,
                debug=debug,
                chunk_duration=15,
                output_path=output_path
            )

    except Exception as e:
        print(f"Error processing video {video_path}: {e}")

def main():
    parser = argparse.ArgumentParser(description='Run different VLM pipelines.')
    parser.add_argument('-p', '--pipeline', choices=['image', 'video', 'stream'], default='video',
                        help='Select the pipeline to run: image, video, or stream (default: video).')
    parser.add_argument('-i', '--image_path', type=str, default=None,
                        help='Path to the image file (required for image pipeline).')
    parser.add_argument('-v', '--video_path', type=str, default=None,
                        help='Path to the video file (required for video pipeline).')
    parser.add_argument('-s', '--stream_url', type=str, default=None,
                        help='RTSP stream URL link (required for stream pipeline).')
    parser.add_argument('-f', '--video_folder', type=str, default=None,
                        help='Path to a folder containing video files.')
    parser.add_argument('-c', '--config_path', type=str, default='config/model/default.yaml',
                        help='Path to the config file.')
    parser.add_argument('-o', '--output_path', type=str, default='/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/processed/office/qween_2b/results.json',
                        help='Path to the output alerts JSON file.')
    parser.add_argument('-e', '--eval_dir', type=str, default='data/eval/hugging_face',
                        help='Directory to store evaluation data.')
    parser.add_argument('-d', '--debug', action='store_true', help='Sets debug mode.')

    args = parser.parse_args()

    # Load configuration file
    with open(args.config_path, 'r') as config_file:
        config = load_yaml(config_file)

    # Extract paths from config
    actions_path = config.get('app', {}).get('actions', 'config/prompt/actions.json')
    prompts_path = config.get('app', {}).get('prompts', 'config/prompt/prompts.json')    
    context_path = config.get('app', {}).get('context', 'config/prompt/context.json') 

    if args.pipeline == 'video':
        if args.video_folder:
            # Process all video files in the folder and its subfolders
            for root, dirs, files in os.walk(args.video_folder):
                for file_name in files:
                    if file_name.endswith(('.mp4', '.avi', '.mkv')):
                        video_path = os.path.join(root, file_name)
                        print(f"Processing video: {video_path}")
                        process_video(video_path, args.config_path, actions_path, prompts_path,
                                    context_path, args.eval_dir, args.output_path, args.debug)

        elif args.video_path:
            # Process a single video file
            print(f"Processing video: {args.video_path}")
            process_video(args.video_path, args.config_path, actions_path, prompts_path,
                          context_path, args.eval_dir, args.output_path, args.debug)
        else:
            print("Error: --video_path or --video_folder is required for the video pipeline.")
            sys.exit(1)

    elif args.pipeline == 'image':
        if not args.image_path:
            print("Error: --image_path is required for the image pipeline.")
            sys.exit(1)
        analysis = pipeline_image_basic.run_pipeline(args.config_path, args.image_path, actions_path, prompts_path,
                                                     context_path, args.debug)
        print("Image analysis completed.")

    elif args.pipeline == 'stream':
        if not args.stream_url:
            print("Error: --stream_url is required for the stream pipeline.")
            sys.exit(1)
        print(f"Processing stream: {args.stream_url}")
        pipeline_video_stream_lean.run_stream_pipeline(
            config_path=args.config_path,
            stream_url=args.stream_url,
            actions_path=actions_path,
            prompts_path=prompts_path,
            context_path=context_path,
            output_path=args.output_path,
        )
    else:
        print("Invalid pipeline choice. Use --help for options.")
        sys.exit(1)

if __name__ == "__main__":
    main()
