import sys
import os
# Add the project root directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.nx.nx_api import NxAPI
import time
import json

def test_single_alert(nx_api):
    """Test a single alert to verify NX API connectivity and event sending"""
    print("\nTesting single alert...")
    
    test_alert = {
        "stream_number": 3,
        "chunk_start": "00:00:05",
        "chunk_end": "00:00:10",
        "analysis": {
            "is_suspicious": "Yes",
            "suspicious_activity": "TEST ALERT - Please Ignore",
            "level_of_suspicion": 1,
            "reasoning": "This is a test alert to verify system functionality"
        }
    }

    try:
        # Send Active alert
        success = nx_api.send_event(
            "userdefinedevent",
            "Test Alert",
            f"TEST - Stream {test_alert['stream_number']}: {test_alert['analysis']['suspicious_activity']}",
            "Active"
        )
        print(f"Sending Active alert: {'Success' if success else 'Failed'}")
        
        time.sleep(2)  # Wait 2 seconds
        
        # Send Inactive alert
        success = nx_api.send_event(
            "userdefinedevent",
            "Test Alert Reset",
            f"TEST - Reset alert for Stream {test_alert['stream_number']}",
            "Inactive"
        )
        print(f"Sending Inactive alert: {'Success' if success else 'Failed'}")
        
        return success
    except Exception as e:
        print(f"Error in test_single_alert: {e}")
        return False

def test_alert_sequence(nx_api):
    """Test the full alert sequence with shorter intervals"""
    print("\nTesting alert sequence...")
    
    try:
        # Load alerts
        with open('alerts.json', 'r') as f:
            data = json.load(f)
            alerts = data['alerts']
        
        print(f"Loaded {len(alerts)} alerts from alerts.json")
        
        # Test each alert with 5-second intervals
        for i, alert in enumerate(alerts):
            print(f"\nTesting alert {i+1}/{len(alerts)}")
            print(f"Stream {alert['stream_number']}: {alert['analysis']['suspicious_activity']}")
            
            # Send Active alert
            success = nx_api.send_event(
                "userdefinedevent",
                "Test Sequence Alert",
                f"Stream {alert['stream_number']}: {alert['analysis']['suspicious_activity']} "
                f"(Level {alert['analysis']['level_of_suspicion']})\n"
                f"Reason: {alert['analysis']['reasoning']}",
                "Active"
            )
            print(f"Sending Active alert: {'Success' if success else 'Failed'}")
            
            time.sleep(2)  # Wait 2 seconds
            
            # Send Inactive alert
            success = nx_api.send_event(
                "userdefinedevent",
                "Test Sequence Reset",
                f"Reset alert for Stream {alert['stream_number']}",
                "Inactive"
            )
            print(f"Sending Inactive alert: {'Success' if success else 'Failed'}")
            
            time.sleep(3)  # Wait 3 seconds before next alert
            
        return True
    except Exception as e:
        print(f"Error in test_alert_sequence: {e}")
        return False

def main():
    # Initialize NX API and login
    nx_api = NxAPI()
    
    # NX Witness server details
    nx_server_url = "https://192.168.10.151:7001"
    nx_username = "admin"
    nx_password = "Deepedge@2118"
    
    print("Starting alert system test...")
    print(f"Connecting to NX Witness at {nx_server_url}")
    
    # Test connection and login
    if nx_api.login(nx_server_url, nx_username, nx_password):
        print("Successfully logged into NX Witness")
        
        try:
            # Test 1: Single alert
            if test_single_alert(nx_api):
                print("\nSingle alert test passed!")
            else:
                print("\nSingle alert test failed!")
            
            time.sleep(2)  # Wait between tests
            
            # Test 2: Alert sequence
            if test_alert_sequence(nx_api):
                print("\nAlert sequence test completed!")
            else:
                print("\nAlert sequence test failed!")
                
        finally:
            # Cleanup
            print("\nCleaning up...")
            nx_api.delete_current_session()
            print("Test completed!")
    else:
        print("Failed to login to NX Witness")

if __name__ == "__main__":
    main()
