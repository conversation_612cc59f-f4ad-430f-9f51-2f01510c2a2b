import json
import os
import spacy
from openai import OpenAI

client = OpenAI(api_key=api_key)
import yaml


#Load spaCy's English NLP model (ensure it's installed: `pip install spacy` & `python -m spacy download en_core_web_sm`)
#nlp = spacy.load("en_core_web_sm")


###filtering the newkeyword with respect to its significance###

# Load configuration from the config.json file
def load_config(config_path="config/model/default.yaml"):
    with open(config_path, "r") as file:
        config = yaml.safe_load(file)
    return config


# Function to send a prompt to GPT
def ask_gpt(prompt):
    config = load_config()
    api_key = config["language_model"]["api_key"]
    model = config["language_model"]["model_name"]

      # Set the API key

    try:
        response = client.chat.completions.create(model=model,
        messages=[{"role": "system", "content": "You are a helpful assistant."},
                  {"role": "user", "content": prompt}])
        return response.choices[0].message.content

    except Exception as e:
        return f"Error: {str(e)}"



#Load JSON Data (Keyword Dictionary)
def load_json(json_path):
    """Load JSON data from a file."""
    if os.path.exists(json_path):
        with open(json_path, "r") as file:
            return json.load(file)
    else:
        raise FileNotFoundError(f"JSON file not found: {json_path}")


#Save Updated JSON Data
def save_json(json_path, data):
    """Save updated keyword dictionary to JSON file."""
    with open(json_path, "w") as file:
        json.dump(data, file, indent=4)
    print(f"Updated keywords saved to {json_path}")


#Find the Best Matching Class for the Given Use Case
def find_best_class(use_case, usecase_actions_mapped):
    """Finds the correct related class from usecase_actions_mapped based on the given use case."""
    for related_class, usecases in usecase_actions_mapped.items():
        if use_case in usecases:
            return related_class
    return None


#Extract Verbs & Other Words from Tokens (Handles Words with Multiple Meanings)
def classify_tokens(tokens):
    """
    Classifies tokens into verbs and other words using NLP.

    Parameters:
        tokens (list): List of words (not characters).

    Returns:
        tuple: (set of verbs, set of other words)
    """
    verbs = set()
    other_words = set()

    # Ensure tokens are space-separated words, not individual characte
    print("Input Text:", tokens)  # Debugging print to check input

    # Process using spaCy
    doc = nlp(tokens)

    for token in doc:
        # If the token is both a noun & a verb (like "block"), prioritize as a verb
        if token.pos_ == "VERB":
            verbs.add(token.text.lower())
        else:
            other_words.add(token.text.lower())

    return verbs, other_words


#Match Tokens & Update JSON
def match_tokens_to_class(use_case, tokens, keyword_dict, usecase_actions_mapped, json_path):
    """
    Matches VLM output tokens to a related class and checks if they match its keywords.
    If new keywords are found, they are added to the JSON file.

    Parameters:
        use_case (str): The use case name (e.g., 'burglary').
        tokens (set): Set of tokens from the VLM model.
        keyword_dict (dict): The dictionary containing keyword mappings.
        usecase_actions_mapped (dict): The dictionary mapping use cases to their related classes.
        json_path (str): Path to the keyword dictionary JSON file.

    Returns:
        dict: Mapped results with class, keyword matches, and status.
    """


    # Get the related class from usecase_actions_mapped
    matched_class = find_best_class(use_case, usecase_actions_mapped)

    result = {
        "video_directory": use_case,
        "matched_class": matched_class,
        "primary_matches": [],
        "secondary_matches": [],
        "new_primary_keywords": [],
        "new_secondary_keywords": [],
        "status": "No Matching Class"
    }


    if matched_class and matched_class in keyword_dict:
        primary_keywords = set(keyword_dict[matched_class].get("primary_keywords", []))
        secondary_keywords = set(keyword_dict[matched_class].get("secondary_keywords", []))


        # Extract verbs & other words
        verbs, other_words = classify_tokens(tokens)

        # Find matching verbs (Primary Keywords)
        primary_match = primary_keywords.intersection(verbs)
        new_primary = verbs - primary_keywords  # New primary keywords

        # Find matching non-verbs (Secondary Keywords)
        secondary_match = secondary_keywords.intersection(other_words)
        new_secondary = other_words - secondary_keywords  # New secondary keywords

        # Update result
        result = {
            "primary_matches": list(primary_match),
            "secondary_matches": list(secondary_match),
            "new_primary_keywords": list(new_primary),
            "new_secondary_keywords": list(new_secondary),
            "status": "Matched"
        }


        # If there are new keywords, update the JSON
        if new_primary or new_secondary:
            print(f"New Primary Keywords: {new_primary}")
            print(f"New Secondary Keywords: {new_secondary}")


            #call gpt function to filter new keywords
            new_primary = ask_gpt(f"I am giving you some keywords {new_primary} and the matched class or usecase {matched_class}. just ouput those words from new_primary list which are more relevant with respect to the matched_class")
            new_secondary = ask_gpt(f"I am giving you some keywords {new_secondary} and the matched class or usecase {matched_class}. just ouput those words from new_primary list which are more relevant with respect to the matched_class")

            print("new_one->", new_primary)
            print("new-second->", new_secondary)

            # Ensure the keyword_dict structure exists
            if "primary_keywords" not in keyword_dict[matched_class]:
                keyword_dict[matched_class]["primary_keywords"] = []
            if "secondary_keywords" not in keyword_dict[matched_class]:
                keyword_dict[matched_class]["secondary_keywords"] = []

            # Convert existing lists to sets, update them, then store back as lists
            keyword_dict[matched_class]["primary_keywords"] = list(set(keyword_dict[matched_class]["primary_keywords"]).union(new_primary))
            keyword_dict[matched_class]["secondary_keywords"] = list(set(keyword_dict[matched_class]["secondary_keywords"]).union(new_secondary))

            # Save updated JSON
            save_json(json_path, keyword_dict)
            print("Pushed new keywords to the JSON file.")

        return result


#Main Function for External Calls
def process_tokens(video_dir, tokens, json_path="config/prompt/actions_pattern.json", keyword_usecase_mapping="config/prompt/usecase_actions_mapping.json"):
    """
    Process tokens and directory name against a keyword JSON dictionary.
    
    Parameters:
        json_path (str): Path to the keyword dictionary JSON file.
        video_dir (str): Full video directory path.
        tokens (list): List of tokens generated by VLM.
    
    Returns:
        dict: Mapped results with class, keyword matches, and status.
    """
    # Load keyword dictionary
    keyword_dict = load_json(json_path)

    # Load use case mappings
    usecase_actions_mapped = load_json(keyword_usecase_mapping)

    # Extract use case name from video directory path
    use_case = os.path.basename(os.path.dirname(video_dir))

    # Perform token matching & update JSON if needed
    match_tokens_to_class(use_case, tokens, keyword_dict, usecase_actions_mapped, json_path)







