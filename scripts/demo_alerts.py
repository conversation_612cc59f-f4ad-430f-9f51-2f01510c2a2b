import sys
import os
import time
import json
from src.nx.nx_api import NxAPI

def time_to_seconds(time_str):
    h, m, s = map(int, time_str.split(':'))
    return h * 3600 + m * 60 + s

def main():
    print("Starting demo alerts script...")
    
    # Initialize NX API
    nx_api = NxAPI()
    nx_server_url = "https://**************:7001"
    nx_username = "admin"
    nx_password = "Deepedge@2118"
    
    print(f"Connecting to NX Witness at {nx_server_url}")
    
    # Login to NX Witness
    if not nx_api.login(nx_server_url, nx_username, nx_password):
        print("Failed to login to NX Witness. Exiting.")
        sys.exit(1)
    
    print("Successfully logged into NX Witness")
    
    try:
        # Load alerts configuration
        print("Loading alerts from alerts.json...")
        with open('alerts.json', 'r') as f:
            data = json.load(f)
        
        alerts = data['alerts']
        start_time = time.time()
        sent_alerts = set()
        
        # Verify timing sequence before starting
        print("\nVerifying alert sequence timing:")
        for i, alert in enumerate(alerts, 1):
            seconds = time_to_seconds(alert['chunk_start'])
            print(f"Alert {i}: Stream {alert['stream_number']} will trigger at {seconds} seconds ({alert['chunk_start']})")
        
        print("\nStarting alert sequence...")
        
        while len(sent_alerts) < len(alerts):
            current_time = time.time() - start_time
            
            for i, alert in enumerate(alerts):
                if i not in sent_alerts:
                    alert_time = time_to_seconds(alert['chunk_start'])
                    if current_time >= alert_time:
                        try:
                            # Log exact timing of alert
                            actual_time = time.strftime("%H:%M:%S", time.gmtime(current_time))
                            print(f"\nTriggering alert at {actual_time} (scheduled for {alert['chunk_start']}):")
                            print(f"Stream {alert['stream_number']}: {alert['analysis']['suspicious_activity']}")
                            
                            # Send Active alert
                            success = nx_api.send_event(
                                "userdefinedevent",
                                "AI Security Alert",
                                f"Stream {alert['stream_number']}: Suspicious Activity Detected: {alert['analysis']['suspicious_activity']} "
                                f"(Level {alert['analysis']['level_of_suspicion']})\n"
                                f"Reason: {alert['analysis']['reasoning']}",
                                "Active"
                            )
                            print(f"Sending Active alert: {'Success' if success else 'Failed'}")
                            
                            time.sleep(1)
                            
                            # Send Inactive state
                            success = nx_api.send_event(
                                "userdefinedevent",
                                "Reseting Event",
                                f"Reset alert for Stream {alert['stream_number']}",
                                "Inactive"
                            )
                            print(f"Sending Inactive alert: {'Success' if success else 'Failed'}")
                            
                            sent_alerts.add(i)
                            
                        except Exception as e:
                            print(f"Error sending alert {i+1}: {e}")
            
            time.sleep(0.1)
        
        print("All alerts have been sent!")
        
    except Exception as e:
        print(f"Error in main loop: {e}")
    finally:
        print("Cleaning up...")
        nx_api.delete_current_session()
        print("Script completed!")

if __name__ == "__main__":
    main()
