import sys
import re
import os
import json
import argparse
import yaml
from src.pipelines import pipeline_image_basic, pipeline_video_stream
from src.utils.stream import create_rtsp_url, load_stream_config
from pdb import set_trace

def append_to_alerts_file(alert, file_path):
    if os.path.exists(file_path):
        with open(file_path, 'r+') as file:
            data = json.load(file)
            data.append(alert)
            file.seek(0)
            json.dump(data, file, indent=4)
    else:
        with open(file_path, 'w') as file:
            json.dump([alert], file, indent=4)

def clean_json_response(response):
    cleaned_response = re.sub(r'```json\n|\n```', '', response)
    cleaned_response = cleaned_response.replace("'", '"')
    return json.loads(cleaned_response)

def load_config(config_path):
    with open(config_path, 'r') as file:
        return yaml.safe_load(file)


def main():
    parser = argparse.ArgumentParser(description='Run different VLM pipelines.')
    parser.add_argument('-p', '--pipeline', choices=['image', 'video', 'stream'], default='stream', help='Select the pipeline to run: image, video, or stream (default: stream).')
    parser.add_argument('-i', '--image_path', type=str, default='C:/Users/<USER>/Downloads/images.png', help='Path to the image file (default: C:/Users/<USER>/Downloads/default_image.png).')
    parser.add_argument('-v', '--video_path', type=str, default='data/samples/input_video7.mp4', help='Path to the video file.')
    parser.add_argument('-c', '--config_path', type=str, default='config/model/default.yaml', help='Path to the config file.')
    parser.add_argument('-a', '--actions_path', type=str, default='config/prompt/actions.json', help='Path to the actions JSON file.')
    parser.add_argument('-pr', '--prompts_path', type=str, default='config/prompt/prompts.json', help='Path to the prompts JSON file.')
    parser.add_argument('-ctx', '--context_path', type=str, default='config/prompt/context.json', help='Path to the context JSON file.')
    parser.add_argument('-o', '--output_path', type=str, default='data/processed/alerts.json', help='Path to the output alerts JSON file.')

    args = parser.parse_args()

    if args.pipeline == 'image':
        if not args.image_path:
            print("Error: --image_path is required for the image pipeline.")
            sys.exit(1)
        analysis = pipeline_image_basic.run_pipeline(args.config_path, args.image_path, args.actions_path, args.prompts_path, args.context_path)
    elif args.pipeline in ['video', 'stream']:
        if args.pipeline == 'video' and not args.video_path:
            print("Error: --video_path is required for the video pipeline.")
            sys.exit(1)
        analysis = pipeline_video_stream.run_pipeline(args.config_path, args.actions_path, args.prompts_path, args.context_path)
        input_path = args.video_path if args.pipeline == 'video' else create_rtsp_url(load_stream_config(args.config_path))
        for idx, response in enumerate(analysis):
            cleaned_response = clean_json_response(response.content)
            alert_entry = {"input": input_path, "output": cleaned_response, "window": idx + 1}
            # append_to_alerts_file(alert_entry, args.output_path)
    else:
        print("Invalid pipeline choice. Use --help for options.")
        sys.exit(1)

if __name__ == "__main__":
    main()