import yaml
import json
from openai import OpenAI
import numpy as np
from src.vector_stores.vector_store_factory import VectorStoreFactory
from src.utils.helpers import load_json
from src.utils.questions import QuestionsUtils
import os
import argparse
from src.utils.yaml_utils import load_yaml


def load_config(config_path):
    with open(config_path, "r") as file:
        return load_yaml(file)

def get_embedding(text, client, model="text-embedding-3-small"):
    text = text.replace("\n", " ")
    embedding = client.embeddings.create(input=[text], model=model).data[0].embedding
    return embedding

def vectorize_data(config_path, actions_path):
    config = load_config(config_path)
    
    client = OpenAI(api_key=config['openai_api_key'])
    vector_store = VectorStoreFactory.create_vector_store(config)
    
    all_embeddings = []
    all_ids = []

    # Initialize QuestionsUtils
    questions_utils = QuestionsUtils(actions_path)

    # Get deconstructor questions with context
    deconstructor_questions_with_context = questions_utils.get_deconstructor_questions_with_context()

    # Vectorize deconstructor questions with context
    for i, q in enumerate(deconstructor_questions_with_context):
        embedding = get_embedding(q, client, model=config['embedding']['model'])
        if not vector_store.embedding_exists(embedding):  # Check for duplicates
            all_embeddings.append(embedding)
            all_ids.append(i)
    
    # Vectorize constructor actions and context
    constructor_actions = questions_utils.get_constructor_actions()
    
    start_id = len(all_ids)
    
    for i, c in enumerate(constructor_actions):
        embedding = get_embedding(c, client,  model=config['embedding']['model'])
        if not vector_store.embedding_exists(embedding):  # Check for duplicates
            all_embeddings.append(embedding)
            all_ids.append(start_id + i)
        
    print("Questions and actions being vectorized:", deconstructor_questions_with_context, constructor_actions)

    # Ensure directory exists
    vector_store_path = config['vector_store']['path']
    directory = os.path.dirname(vector_store_path)
    if not os.path.exists(directory):
        os.makedirs(directory)
    
    # Add vectors to the store
    vector_store.add_vectors(np.array(all_embeddings), np.array(all_ids))
    vector_store.save()
    
    print("Relevant data vectorized and stored in vector store.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Vectorize data for VLM pipeline.')
    parser.add_argument('-c', '--config_path', type=str, default='config/model/default.yaml',
                        help='Path to the config file.')

    args = parser.parse_args()

    # Load the config file
    with open(args.config_path, 'r') as config_file:
        config = load_yaml(config_file)

    actions_path = config.get('app', {}).get('actions', 'config/prompt/actions.yaml')
    
    vectorize_data(args.config_path, actions_path)
