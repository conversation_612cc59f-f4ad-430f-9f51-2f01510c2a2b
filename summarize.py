import os
import ast


def should_ignore(name):
    ignore_list = ['.git', '__pycache__', 'node_modules', 'venv', '.idea', '.vscode']
    return name.startswith('.') or name in ignore_list


def get_directory_tree(start_path):
    tree = []
    for root, dirs, files in os.walk(start_path):
        dirs[:] = [d for d in dirs if not should_ignore(d)]
        level = root.replace(start_path, '').count(os.sep)
        indent = ' ' * 4 * level
        tree.append(f"{indent}{os.path.basename(root)}/")
        for file in files:
            if not should_ignore(file) and file.endswith('.py'):
                tree.append(f"{indent}    {file}")
    return '\n'.join(tree)


def get_code_overview(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        try:
            content = file.read()
        except UnicodeDecodeError:
            return "Error reading file"

    try:
        tree = ast.parse(content)
    except SyntaxError:
        return "Error parsing file"

    overview = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                overview.append(f"Import: {alias.name}")
        elif isinstance(node, ast.ImportFrom):
            for alias in node.names:
                overview.append(f"From {node.module} import {alias.name}")
        elif isinstance(node, ast.ClassDef):
            overview.append(f"Class: {node.name}")
            if node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Str):
                overview.append(f"  Description: {node.body[0].value.s.strip()}")
        elif isinstance(node, ast.FunctionDef):
            overview.append(f"Function: {node.name}")
            if node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Str):
                overview.append(f"  Description: {node.body[0].value.s.strip()}")

    return '\n'.join(overview)


def generate_project_overview(project_path):
    tree = get_directory_tree(project_path)
    overview = [f"Project Structure:\n{tree}\n\nCode Overviews:"]

    for root, dirs, files in os.walk(project_path):
        dirs[:] = [d for d in dirs if not should_ignore(d)]
        for file in files:
            if not should_ignore(file) and file.endswith('.py'):
                file_path = os.path.join(root, file)
                file_overview = get_code_overview(file_path)
                if file_overview:
                    overview.append(f"\n{file_path}:\n{file_overview}")

    return '\n'.join(overview)


# Usage
project_path = '/home/<USER>/rmidigudla/deconstructur_lean/lluminaai'
output = generate_project_overview(project_path)

with open(project_path + "/data/"+ 'project_overview.txt', 'w') as f:
    f.write(output)

print("Project overview has been saved to 'project_overview.txt'")