{"basic_questions": [{"question": "Describe the main action in 3-4 words (max).", "context": "Identify the most prominent or unusual activity occurring in the scene."}, {"question": "What type of office area? (cubicle/meeting room/hallway/lobby/other)", "context": "Specify the visible office area to understand the context of the actions."}, {"question": "How many people are there? (number). Just give a number.", "context": "Provide an exact count of individuals in the frame."}, {"question": "Any abnormal activity? Is anyone fighting? or hiding? or group forming? or vandalism? or anyone breaking company property?", "context": "Identify if there's any clearly unusual behavior for an office setting."}, {"question": "Any signs of explosion? Are there visible flames, debris, flying objects?", "context": "Look for indications of an explosion, such as flames, smoke, or scattered debris."}, {"question": "Any signs of forced entry? Are doors or windows damaged?", "context": "Look for evidence of forced entry, such as broken locks, windows, or doors, which are common signs of a burglary."}], "constructor": ["0.Detected physical altercations - fights, brawls, or aggressive conflicts in any space.", "1.Detected vandalism - property defacement, graffiti, or equipment damage.", "2.Detected robbery attempts - forceful or threatening theft of money, valuables, or goods.", "3.Detected burglary incidents - unauthorized entry into buildings, properties, or vehicles for theft or damage.", "4.Detected shoplifting - concealed items, retail theft, or suspicious behavior in stores.", "5.Detected falling incidents - slips, trips, collapses, or other hazards in risky areas.", "6.Detected delivery or logistics issues - blocked pathways, delays, or misplaced shipments.", "7.Detected unusual crowd behavior - overcrowding, long queues, or pathway obstructions.", "8.Detected child safety concerns - unattended children, wandering kids, or proximity to hazards.", "9.Detected potential hazards - spills, debris, slippery surfaces, or broken objects.", "10.Detected suspicious baggage - abandoned bags, parcels, or unattended packages.", "11.Detected vehicle obstruction - parked vehicles blocking pathways or entrances.", "12.Detected tampering - unauthorized manipulation of devices or property.", "13.Detected security breaches - unauthorized access, intrusions, or alarm tampering.", "14.Detected customer behavior issues - suspicious, indecisive, or erratic actions.", "15.Detected improper waste disposal - trash accumulation, overflowing bins, or misplaced waste.", "16.Detected suspicious group behavior - coordinated or unusual activities by groups.", "17.Detected skimming devices - fraudulent ATM behavior or installation of skimming devices.", "18.Detected road and pathway obstructions - vehicles blocking roads, driveways, or important access points", "19.Detected indecisive subject - exhibiting prolonged hesitation and wavering decisions."], "action_patterns": {"fight_related": {"primary_keywords": ["punch", "kick", "hit", "grapple", "shove", "wrestle", "fight"], "secondary_keywords": ["person", "people", "group", "crowd", "gang", "street", "arena", "club", "customer"], "use_case": "physical altercations - fights, brawls, or aggressive conflicts in any space", "instruction": "Monitor for fights or physical altercations occurring in public or private spaces. Track individuals involved and check for weapons or crowd escalation.", "severity": 3}, "vandalism_related": {"primary_keywords": ["spray", "smash", "break", "kick", "throw", "scribble"], "secondary_keywords": ["wall", "window", "glass", "car", "sign", "door", "storefront", "statue"], "use_case": "vandalism - property defacement, graffiti, or equipment damage", "instruction": "Monitor areas for acts of vandalism or property damage. Pay special attention to spray painting, breaking objects, or defacing public property.", "severity": 2}, "robbery_related": {"primary_keywords": ["steal", "snatch", "take", "grab", "attack", "force", "threaten", "rob"], "secondary_keywords": ["money", "jewelry", "bag", "wallet", "phone", "cash", "store", "vehicle", "cashier"], "use_case": "robbery attempts - forceful or threatening theft of money, valuables, or goods", "instruction": "Monitor for robbery attempts, especially involving threats or force. Check if individuals are snatching items and fleeing or engaging in violent interactions.", "severity": 3}, "burglary_related": {"primary_keywords": ["break", "enter", "trespass", "unlock", "pry", "sneak"], "secondary_keywords": ["home", "office", "building", "store", "room", "vault", "apartment", "warehouse"], "use_case": "burglary incidents - unauthorized entry into buildings, properties, or vehicles for theft or damage", "instruction": "Monitor for signs of burglary, such as unauthorized entry or suspicious activities near properties. Detect forced entry attempts or unusual movements near restricted areas.", "severity": 3}, "shoplifting_related": {"primary_keywords": ["shoplift", "steal", "theft", "hide", "conceal", "take", "grab", "remove", "switch"], "secondary_keywords": ["item", "product", "store", "mall", "counter", "aisle", "bag", "clothing", "merchandise", "cashier"], "use_case": "shoplifting - concealed items, retail theft, or suspicious behavior in stores", "instruction": "Monitor stores and retail areas for shoplifting behaviors. Pay attention to individuals concealing items or acting suspiciously near exits or aisles.", "severity": 2}, "falling_related": {"primary_keywords": ["fall", "slip", "trip", "collapse", "stumble", "tumble"], "secondary_keywords": ["floor", "stairs", "escalator", "pathway", "ledge", "obstacle"], "use_case": "falling incidents - slips, trips, collapses, or other hazards in risky areas", "instruction": "Monitor for incidents of falling, especially near hazardous areas or obstacles. Detect if assistance is needed and check if the fall is accidental or caused by external factors.", "severity": 2}, "unattended_bags_related": {"primary_keywords": ["leave", "drop", "place", "forget"], "secondary_keywords": ["bag", "luggage", "backpack", "parcel", "suitcase", "box"], "use_case": "suspicious baggage - abandoned bags, parcels, or unattended packages", "instruction": "Monitor for unattended or abandoned bags and items that could pose a safety or security risk. Observe for individuals leaving packages in crowded areas and walking away quickly.", "severity": 3}, "queue_and_crowd_related": {"primary_keywords": ["queue", "line", "gather", "cluster", "form"], "secondary_keywords": ["entrance", "counter", "pathway", "area", "check-in", "exit"], "use_case": "unusual crowd behavior - overcrowding, long queues, or pathway obstructions", "instruction": "Monitor queues and crowds for overcrowding, abandonment, or mismanagement. Identify unusual gathering patterns or crowd distress.", "severity": 1}, "group_behavior_related": {"primary_keywords": ["group", "gather", "coordinate", "assemble", "loiter"], "secondary_keywords": ["person", "people", "mob", "crowd", "gang", "team"], "use_case": "suspicious group behavior - coordinated or unusual activities by groups", "instruction": "Monitor groups of people for suspicious gatherings or coordinated behavior. Detect whether individuals are loitering or engaging in unusual communication patterns.", "severity": 2}, "waste_disposal_related": {"primary_keywords": ["dispose", "litter", "trash", "dump", "discard", "spill"], "secondary_keywords": ["waste", "garbage", "bin", "trash", "container"], "use_case": "improper waste disposal - trash accumulation, overflowing bins, or misplaced waste", "instruction": "Monitor waste disposal areas for improper disposal or overflowing trash bins. Identify individuals littering in restricted zones.", "severity": 1}, "child_safety_related": {"primary_keywords": ["wander", "unattended", "lost", "stray", "run", "play"], "secondary_keywords": ["road", "escalator", "hazard", "danger", "playground", "crosswalk"], "use_case": "child safety concerns - unattended children, wandering kids, or proximity to hazards", "instruction": "Monitor children for wandering unattended or moving near hazards. Detect if a child appears lost or is near dangerous locations like roads or stairways.", "severity": 2}, "delivery_related": {"primary_keywords": ["block", "delay", "disrupt", "unload", "transport"], "secondary_keywords": ["goods", "pathway", "entrance", "warehouse", "shipment", "truck"], "use_case": "delivery or logistics issues - blocked pathways, delays, or misplaced shipments", "instruction": "Monitor deliveries and vehicle pathways for delays, blockages, or disruptions. Identify unauthorized access to packages or transportation obstructions.", "severity": 1}, "security_incident_related": {"primary_keywords": ["tamper", "breach", "intrude", "disable", "hack", "force", "shoot", "shake", "arrest", "climb", "throw", "jump", "crawl"], "secondary_keywords": ["device", "equipment", "camera", "lock", "network", "door", "vehicle", "suspect", "fence", "object", "street", "window", "wall"], "use_case": "security breaches - unauthorized access, intrusions, or alarm tampering", "instruction": "Monitor for security incidents like tampering with equipment or unauthorized access. Identify suspicious activity near cameras, locks, or sensitive areas.", "severity": 3}, "atm_behavior_related": {"primary_keywords": ["install", "tamper", "modify", "attach", "remove", "cover"], "secondary_keywords": ["atm", "machine", "device", "skimmer", "keyboard", "card slot"], "use_case": "skimming devices - fraudulent ATM behavior or installation of skimming devices", "instruction": "Monitor ATMs for suspicious activities, such as installing skimming devices. Observe individuals tampering with the ATM keypad, card slot, or screen.", "severity": 3}}, "deconstructor": [{"question": "Is anyone putting objects into drawers or under furniture? (yes/no/unknown)", "context": "Look for people hiding items."}, {"question": "Is anyone placing objects in their pocket, bag, or clothing? (yes/no/unknown)", "context": "Look for objects concealed in clothing or bags."}, {"question": "Is anyone crouching or bending down low? (yes/no/unknown)", "context": "check for individuals trying to hide objects"}, {"question": "Are there any bags left alone in the room? (yes/no/unknown)", "context": "Check for unattended bags."}, {"question": "Is anyone carrying large equipment? (yes/no/unknown)", "context": "Look for people moving big items."}, {"question": "Are there objects on the floor that don't belong? (yes/no/unknown)", "context": "Spot items in unusual places."}, {"question": "Are three or more people standing very close together? (yes/no/unknown)", "context": "Look for small groups."}, {"question": "Is anyone touching another person? (yes/no/unknown)", "context": "Check for physical contact."}, {"question": "Is anyone raising their arms high towards another person? (yes/no/unknown)", "context": "Look for raised arms."}, {"question": "Is anyone kicking or lifting their legs towards another person? (yes/no/unknown)", "context": "Look for kicking, jumping, or any significant leg movements."}, {"question": "Is anyone grabbing or pulling another person's clothing", "context": "Look for aggressive grabbing or pulling of clothing."}, {"question": "Is anyone pointing an object towards another person? (yes/no/unknown)", "context": "Look for gestures involving objects aimed or directed at others."}, {"question": "Is anyone lying on the floor? (yes/no/unknown)", "context": "Check for people on the ground."}, {"question": "Is anyone holding onto furniture or other objects? (yes/no/unknown)", "context": "Look for pre-fall behavior"}, {"question": "Are any arms or legs stretched out awkwardly? (yes/no/unknown)", "context": " Look for signs of trying to stop a fall."}, {"question": "Is anyone holding their head or chest? (yes/no/unknown)", "context": "Look for signs of discomfort."}, {"question": "Is anyone on their knees and hands on the ground? (yes/no/unknown", "context": "Look for signs of someone catching themselves after a fall"}, {"question": "Can you see any visible injuries on anyone? (yes/no/unknown)", "context": "Check for signs of harm."}, {"question": "Is anyone breaking objects? (yes/no/unknown)", "context": "Check for damage to items."}, {"question": "Are any individuals using tools to break objects? (yes/no/unknown)", "context": "Look for items like hammers, crowbars, or other implements"}, {"question": "Is debris visible on the ground near a damaged object? (yes/no/unknown)", "context": "Check for fragments, rubble, or broken pieces."}, {"question": "Are there visible signs of damage to vehicles? (yes/no/unknown)", "context": "Check for broken windows, scratches, or damaged tires."}, {"question": "Is there graffiti or spray paint visible? (yes/no/unknown)", "context": "Look for writing, tags, or drawings on surfaces."}, {"question": "Is anyone's face covered by a mask or hood? (yes/no/unknown)", "context": "Look for hidden faces."}, {"question": "Is anyone cruching or bending down low? (yes/no/unknown)", "context": "Look for unusual postures."}, {"question": "Is anyone holding anything that looks like a weapon? (yes/no/unknown)", "context": "Check for dangerous objects."}, {"question": "Are there any individuals with both hands up while standing? (yes/no/unknown)", "context": "Look for gestures of surrender or compliance."}, {"question": "Are safes, or wallets being accessed? (yes/no/unknown)", "context": "Check for signs of theft during a robbery"}, {"question": "Is there smoke or fire visible? (yes/no/unknown)", "context": "Look for signs of fire or smoke in the area."}, {"question": "Is there any flash or burst of light in the frame? (yes/no/unknown)", "context": "Identify visual evidence of an explosion or detonation."}, {"question": "Are walls or surfaces blackened or charred? (yes/no/unknown)", "context": "Check for signs of burning or explosion effects on structures."}, {"question": "Are any objects falling or collapsing? (yes/no/unknown)", "context": "Look for signs of structures or items breaking down due to an explosion"}, {"question": "Is there ash or burnt material visible? (yes/no/unknown)", "context": "Check for residue left after a fire or explosion."}, {"question": "Are there any damaged vehicles in the scene? (yes/no/unknown)", "context": "Look for signs of explosions affecting cars or other vehicles."}, {"question": "Is anyone using water spray, a fire extinguisher, or foam? (yes/no/unknown)", "context": "Check for individuals attempting to extinguish a fire."}, {"question": "Are there any broken locks, windows, or doors visible? (yes/no/unknown)", "context": "Look for signs of forced entry in the frame."}, {"question": "Is anyone carrying tools or large bags? (yes/no/unknown)", "context": "Check for suspicious items that could be used in burglary."}, {"question": "Are there items scattered on the floor? (yes/no/unknown)", "context": "Look for unusual mess indicating theft or disturbance."}]}