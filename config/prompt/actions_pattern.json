{"fight_related": {"primary_keywords": ["punch", "kick", "hit", "grapple", "shove", "wrestle", "fight", "brawl", "clash", "scuffle", "attack", "strike", "jab", "pummel", "scrap"], "secondary_keywords": ["person", "people", "group", "crowd", "gang", "street", "arena", "club", "customer", "bystander", "participant", "offender", "pub", "alley", "venue"], "use_case": "physical altercations - fights, brawls, or aggressive conflicts in any space", "instruction": "Monitor for fights or physical altercations occurring in public or private spaces. Track individuals involved and check for weapons or crowd escalation."}, "vandalism_related": {"primary_keywords": ["spray", "smash", "break", "kick", "throw", "scribble", "deface", "graffiti", "etch", "scratch", "scrawl", "vandalize", "damage", "smear", "scrape"], "secondary_keywords": ["wall", "window", "glass", "car", "sign", "door", "storefront", "statue", "fence", "building", "bridge", "bus shelter", "billboard", "bench", "gate"], "use_case": "vandalism - property defacement, graffiti, or equipment damage", "instruction": "Monitor areas for acts of vandalism or property damage. Pay special attention to spray painting, breaking objects, or defacing public property."}, "robbery_related": {"primary_keywords": ["attack", "force", "threaten", "rob", "mug", "ambush", "seize", "overpower", "commandeer", "intimidate", "demand"], "secondary_keywords": ["money", "jewelry", "bag", "wallet", "phone", "cash", "store", "vehicle", "cashier", "safe", "credit cards", "electronics", "documents", "valuables", "assets"], "use_case": "robbery attempts - forceful or threatening theft of money, valuables, or goods", "instruction": "Monitor for robbery attempts that involve force or threats. Focus on scenarios where individuals use aggression to overpower or intimidate victims."}, "burglary_related": {"primary_keywords": ["enter", "trespass", "sneak", "unlock", "break", "climb", "pick", "smash", "force", "disable", "circumvent"], "secondary_keywords": ["apartment", "warehouse", "store", "home", "building", "vault", "room", "office", "car", "condo", "mansion", "garage", "shed", "estate", "facility"], "use_case": "burglary incidents - unauthorized entry into buildings, properties, or vehicles for theft or damage", "instruction": "Monitor for signs of burglary such as forced entry and suspicious activities near properties. Detect attempts of unauthorized access aimed at theft."}, "shoplifting_related": {"primary_keywords": ["shoplift", "theft", "hide", "conceal", "pilfer", "lift", "swipe", "remove"], "secondary_keywords": ["item", "product", "store", "mall", "counter", "aisle", "bag", "clothing", "merchandise", "shelf", "display", "goods", "packaging", "accessory", "jewelry"], "use_case": "shoplifting - concealed items, retail theft, or suspicious behavior in stores", "instruction": "Monitor retail areas for shoplifting behaviors. Focus on individuals concealing or subtly removing merchandise without drawing attention."}, "falling_related": {"primary_keywords": ["fall", "slip", "trip", "collapse", "stumble", "tumble", "fell", "drop", "plummet", "topple", "stagger", "lurch", "reel", "plunge", "falter"], "secondary_keywords": ["floor", "woman", "man", "stairs", "escalator", "pathway", "ledge", "obstacle", "ground", "step", "platform", "landing", "ramp", "pavement", "sidewalk"], "use_case": "falling incidents - slips, trips, collapses, or other hazards in risky areas", "instruction": "Monitor for incidents of falling, especially near hazardous areas or obstacles. Detect if assistance is needed and whether the fall is accidental or induced by external factors."}, "unattended_bags_related": {"primary_keywords": ["leave", "drop", "place", "forget", "abandon", "discard", "deposit", "mislay", "jettison", "forsake", "relinquish", "evacuate", "withdraw", "dump", "depose", "set aside", "misplace", "dismount", "vacate"], "secondary_keywords": ["bag", "luggage", "backpack", "parcel", "suitcase", "box", "duffel", "tote", "briefcase", "knapsack", "satchel", "messenger bag", "carrier", "rucksack", "valise", "sack", "handbag", "carryall", "pannier", "fanny pack", "trunk"], "use_case": "suspicious baggage - abandoned bags, parcels, or unattended packages", "instruction": "Monitor for unattended or abandoned bags that could pose a safety or security risk. Observe for individuals leaving packages in crowded areas and then quickly walking away."}, "queue_and_crowd_related": {"primary_keywords": ["queue", "line", "cluster", "assemble", "congregate", "throng", "flock", "gather", "accumulate"], "secondary_keywords": ["entrance", "counter", "pathway", "area", "check-in", "exit", "lobby", "gate", "hallway", "reception", "booth", "checkpoint", "corridor", "platform", "concourse", "terminal", "doorway", "passage", "atrium", "foyer", "escalator"], "use_case": "crowd and queue issues - situations with long lines, overcrowding, or blocked pathways", "instruction": "Monitor areas for abnormal crowd behavior, including long queues and congestion. Identify signs of mismanagement or distress in gathering patterns."}, "group_behavior_related": {"primary_keywords": ["coordinate", "organize", "conspire", "plan", "plot", "scheme", "convene", "collaborate", "orchestrate"], "secondary_keywords": ["group", "team", "crew", "clique", "gang", "faction", "alliance", "unit", "collective"], "use_case": "suspicious group behavior - coordinated or premeditated activities by groups that may indicate planning or conspiracy", "instruction": "Monitor groups for signs of coordinated or premeditated behavior that may be suspicious. Look for unusual communication or planning signals among group members."}, "waste_disposal_related": {"primary_keywords": ["dispose", "litter", "trash", "dump", "discard", "spill", "toss", "fling", "scatter", "jettison", "unload", "offload", "misdispose", "ditch", "shed", "heave", "chuck", "pitch", "hurl", "lob", "eject"], "secondary_keywords": ["waste", "garbage", "bin", "container", "receptacle", "dumpster", "skip", "wastebin", "dustbin", "wastebasket", "ashcan", "refuse", "rubbish", "trashcan", "barrel", "caddy", "tub", "hopper", "drum"], "use_case": "improper waste disposal - trash accumulation, overflowing bins, or misplaced waste", "instruction": "Monitor waste disposal areas for improper disposal or overflowing trash bins. Alert on instances of littering or dumping in restricted zones."}, "child_safety_related": {"primary_keywords": ["wander", "injure", "unattended", "lost", "stray", "run", "play", "ride", "unsupervised", "roaming", "alone", "escaping", "straying"], "secondary_keywords": ["child", "boy", "kid", "road", "street", "escalator", "hazard", "danger", "playground", "crosswalk", "motorbike", "vehicle", "car", "traffic"], "use_case": "child safety concerns - unattended children, wandering kids, or proximity to hazards", "instruction": "Monitor children for wandering unattended or moving near hazardous areas. Alert if a child appears lost or is near dangerous locations like roads or stairways."}, "delivery_related": {"primary_keywords": ["block", "delay", "disrupt", "unload", "transport", "impede", "stall", "obstruct", "jam", "hinder", "misroute", "reroute", "sabotage", "snarl", "bottleneck", "derail", "halt", "misdeliver", "divert", "complicate"], "secondary_keywords": ["goods", "pathway", "entrance", "warehouse", "shipment", "truck", "depot", "dock", "van", "carrier", "consignment", "parcel", "freight", "manifest", "dispatch", "curb", "yard", "bay", "staging", "terminal", "hub"], "use_case": "delivery or logistics issues - blocked pathways, delays, or misplaced shipments", "instruction": "Monitor for delays or disruptions in delivery and logistics. Identify blockages or unauthorized obstructions in vehicle pathways."}, "security_breach_related": {"primary_keywords": ["tamper", "breach", "intrude", "disable", "hack", "compromise", "bypass", "override", "jerry-rig"], "secondary_keywords": ["device", "equipment", "system", "camera", "lock", "network", "alarm", "panel", "door", "security"], "use_case": "security breaches - unauthorized access, tampering, or interference with security systems", "instruction": "Monitor for security breaches by detecting tampering with equipment or unauthorized access to secure areas. Verify system integrity and alert on potential compromises."}, "indecisive_customer": {"primary_keywords": ["unsure", "hesitant", "waver", "doubt", "indecisive", "dither", "vacillate", "equivocal", "tentative", "falter"], "secondary_keywords": ["uncertain", "confused", "question", "maybe", "ambivalent", "consider", "hesitate", "mull", "noncommittal", "waver"], "use_case": "Retail and customer service scenarios where customers exhibit hesitation or uncertainty in making purchase decisions.", "instruction": "Monitor for verbal or behavioral cues that indicate indecision, such as repeated questions or prolonged hesitation. Proactively engage with the customer by offering clear product information, personalized recommendations, and assistance to help them decide."}, "atm_behavior_related": {"primary_keywords": ["install", "tamper", "modify", "attach", "remove", "cover", "affix", "mount", "override", "hack", "compromise", "jerry-rig", "rig", "alter", "reconfigure", "bypass", "manipulate", "subvert", "disable", "deactivate", "spoof"], "secondary_keywords": ["atm", "machine", "device", "skimmer", "keyboard", "card slot", "terminal", "display", "screen", "keypad", "cash dispenser", "enclosure", "panel", "interface", "reader", "sensor", "casing", "cabinet", "printer", "module", "housing"], "use_case": "skimming devices - fraudulent ATM behavior or installation of skimming devices", "instruction": "Monitor ATMs for suspicious activities, such as installing skimming devices. Observe individuals tampering with the ATM keypad, card slot, or screen."}, "vehicle_blocking_related": {"primary_keywords": ["congest", "delay", "block", "halt", "park", "impede", "stop", "jam", "obstruct"], "secondary_keywords": ["unlock", "lot", "lane", "parking", "location", "truck", "driveway", "road", "intersection", "exit", "yard", "pathway", "violation", "vehicle", "entrance", "front", "car", "traffic"], "use_case": "road and pathway obstructions - vehicles blocking roads, driveways, or important access points", "instruction": "Monitor for vehicles causing obstructions in critical areas such as entrances, exits, or pathways. Alert on prolonged illegal parking or traffic congestion due to parked vehicles."}}