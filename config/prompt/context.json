{"scene_info": "Office surveillance camera stream for detecting unusual activities.", "reasoning": ["Identify health incidents such as fainting or falling by looking for individuals lying flat on the floor without signs of aggressive interaction.", "Detect workplace violence through aggressive interactions, like grabbing, striking, or defensive gestures.", "Robbery indicators include weapons, compliance gestures, or direct theft of items.", "Explosion-related events involve visible smoke, flames, structural damage, or bursts of light.", "Unattended items or conceald objects may indicate suspicious activity.", "Property damage includes breaking objects, graffiti, or tampering with equipment.", "Forced entry is indicated by broken locks, doors, or windows."]}