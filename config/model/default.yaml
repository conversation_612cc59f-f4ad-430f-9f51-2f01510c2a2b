app:
  actions: "config/prompt/actions.json"
  context: "config/prompt/context.json"
  prompts: "config/prompt/prompts.json"


vlm:
  provider: "vllm"  # or "huggingface" ollama or vllm
  model_name: "Qwen/Qwen2-VL-2B-Instruct" # Qwen/Qwen2-VL-2B-Instruct or llava:latest / llava:7b-v1.6-mistral-q8_0 / llava-llama3 / llava:13b / llava-phi3:latest  /deepseek-ai/Janus-Pro-1B


#Matching strategy configuration
matching:
  strategy: "keyword"  # Options:  "keyword", "embedding", "tfidf" ,"hybrid(both keyword & embeddings)"


#new keyword pushing 
keyword_updation: false


embedding:
  model: "sentence-transformers/all-MiniLM-L6-v2"   
  similarity_threshold: 0.5  #Adjust the threshold based on your use case


tfidf:
  similarity_threshold: 0.50 # Pick your desired threshold


openai_api_key: "********************************************************************************************************************************************************************"


language_model:  
  provider: "openai"        
  model_name: "gpt-4o"  
  api_key: "********************************************************************************************************************************************************************"


vector_store:
  provider: "faiss"  # Future: "milvus", "pinecone", etc.
  path: "data/vector_store/{path}/faiss_index"      
  dim: 1536         


#Pipeline Configuration
pipeline:
  video:
    fps: 1  # Frames per second for processing video
    context_interval: 4  # Time interval in seconds for computing context
    sliding_window_duration: 6  # Duration in seconds for the sliding window to call the constructor
    window_overlap: 2  # Overlap in seconds for the sliding window
  deconstructor:
    num_questions: 5  # Number of questions to ask a vlm
  use_lean_deconstructor: true # New flag to switch between implementations 
  deconstructor_lean:
    max_followup_questions: 2  # Limit follow-up questions for speed
    strict_format: true  # Enforce strict response format from VLM 


stream:
  username: "admin"
  password: "Deepedge@2118"
  server_url: "https://**************:7001"  # RTSP stream URL
  ip_address: "**************"
  port: 554
  channel: "03"
  subtype: "00"