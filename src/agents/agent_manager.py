from pydantic import BaseModel

class AgentManager(BaseModel):
    agents: Dict[str, Agent] = {}

    def add_agent(self, agent: Agent):
        self.agents[agent.name] = agent

    def get_agent(self, name: str):
        return self.agents[name]

    def get_agents(self):
        return self.agents

    def get_agent_names(self):
        return list(self.agents.keys())

    def remove_agent(self, name: str):
        del self.agents[name]