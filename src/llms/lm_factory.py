import importlib

class LanguageModelFactory:
    @staticmethod
    def create_language_model(config):
        provider = config['language_model']['provider']
        module_name = f"src.llms.{provider}_lm"
        class_name = provider.capitalize() + "LanguageModel"
        
        try:
            module = importlib.import_module(module_name)
            LanguageModelClass = getattr(module, class_name)
            return LanguageModelClass(api_key=config['language_model']['api_key'], model_name=config['language_model']['model_name'])
        except (ModuleNotFoundError, AttributeError) as e:
            raise ValueError(f"Error loading language model provider {provider}: {e}")
