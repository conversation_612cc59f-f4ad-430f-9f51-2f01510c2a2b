from openai import OpenAI
from .base_lm import BaseLanguageModel

class OpenaiLanguageModel(BaseLanguageModel):
    def __init__(self, api_key, model_name):
        self.client = OpenAI(api_key=api_key)
        self.model_name = model_name
        self.load_model()

    def load_model(self):
        pass

 
    def predict(self, output_format, context_q_and_a, action_list, constructor_header):
        messages = [
            {"role": "system", "content": constructor_header + "\n\n"},
            {"role": "user", "content": f"Output Format:\n{output_format}\n\nList of Possible Actions:\n{action_list}\n\n Here are the Context, Questions and Answers: \n{context_q_and_a }"}
        ]
        print(messages)
        response = self.client.chat.completions.create(
            model=self.model_name,
            messages=messages
        )
        return response.choices[0].message 

