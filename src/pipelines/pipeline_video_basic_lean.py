import os
import re
import time
import json
import base64
import cv2
import numpy as np
from PIL import Image
from io import BytesIO  
from datetime import timedelta
from src.constructor.constructor import Constructor
from src.deconstructor.deconstructor_factory import DeconstructorFactory
from src.utils.yaml_utils import load_yaml
from src.utils.helpers import load_json
from src.utils.video_utils import load_video_frames,standardize_frame
from src.nx.nx_api import NxAPI


#timestamp tracking 
def format_timestamp(seconds):
    """Format seconds into HH:MM:SS format."""
    from datetime import timedelta
    return str(timedelta(seconds=seconds)).split('.')[0].zfill(8)


def encode_frame(frame, provider):
    """
    Convert an image to the appropriate format for Hugging Face or Ollama.

    Args:
        frame: Image (PIL Image or NumPy array).
        provider: "huggingface" or "ollama" (passed from pipeline).

    Returns:
        - For Hugging Face: Returns a processed PIL Image.
        - For Ollama: Returns a Base64-encoded string.
    """

    if frame is None:
        raise ValueError("Invalid frame: Frame is None.")

    # Ensure the frame is in an acceptable format (PIL Image or NumPy array)
    if isinstance(frame, str):
        raise ValueError(f"Unsupported frame type: {type(frame)}. Did you pass a file path instead of an image?")

    if isinstance(frame, Image.Image):
        pil_image = frame  # Already a PIL Image

    elif isinstance(frame, np.ndarray):
        pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))  # Convert OpenCV format to PIL
    else:
        raise ValueError(f"Unsupported frame type: {type(frame)}. Expected PIL Image or NumPy array.")

    # Hugging Face models expect PIL Image
    if provider == "huggingface":
        return pil_image.convert("RGB")  #nsure RGB format for Hugging Face

    
    # Ollama requires Base64 encoding
    elif provider == "ollama":
        # Convert PIL Image to JPEG format and encode as Base64
        buffer = BytesIO()
        pil_image.save(buffer, format="JPEG")
        encoded_image = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return encoded_image  #Return Base64-encoded image
    # image encoding for VLLM
    elif provider == "vllm":
        # Convert CV2 frame to base64 string with optimized quality
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        buffered = BytesIO()
        # Use quality=85 for good balance between quality and size 
        pil_image.save(buffered, format="JPEG", quality=85, optimize=True)
        encoded_image = base64.b64encode(buffered.getvalue()).decode()
        # Clean up to free memory
        del frame_rgb
        return encoded_image

    else:
        raise ValueError(f"Unsupported provider: {provider}. Use 'huggingface' or 'ollama'.")



def get_activity_threshold(cleaned_chunk_analysis, mapping_file_path = "config/prompt/class_priority.json"):
    """
    Loads the JSON mapping file and returns the numeric threshold for the suspicious activity
    found in cleaned_chunk_analysis.
    
    Parameters:
      - cleaned_chunk_analysis: dict containing analysis results, for example:
          {
              "is_suspicious": "Yes",
              "suspicious_activity": "Detected shoplifting - concealed items, retail theft, or suspicious behavior in stores.",
              "level_of_suspicion": 4,
              "reasoning": "..."
          }
      - mapping_file_path: path to the JSON mapping file.
      
    Returns:
      - The numeric threshold for the activity if found in the mapping, else None.
    """
    # Load the mapping from the JSON file.
    try:
        with open(mapping_file_path, 'r') as f:
            mapping = json.load(f)
    except Exception as e:
        print(f"Error loading mapping file: {e}")
        return None
    
    # Normalize mapping keys (all to lowercase).
    
    mapping_normalized = { key.lower(): value for key, value in mapping.items() }
    
    # Extract the suspicious activity text from the analysis.
    suspicious_activity_text = cleaned_chunk_analysis.get("suspicious_activity", "")
    normalized_activity = suspicious_activity_text.lower().strip()
    
    # Remove the "detected " prefix if it exists.
    if normalized_activity.startswith("detected "):
        normalized_activity = normalized_activity[len("detected "):].strip()
    
    # Split on " - " to extract the core activity keyword.
    # For example, "shoplifting - concealed items, retail theft, ..." becomes "shoplifting".
    activity_keyword = normalized_activity.split(" - ")[0].strip()
    
    # Look up the activity keyword in the normalized mapping.
    return mapping_normalized.get(activity_keyword)



def clean_json_response(response):
    try:
        # Remove Markdown-style JSON code blocks and trim whitespace
        response = re.sub(r'```json\n|\n```', '', response).strip()

        # Replace single quotes with double quotes for JSON standardization
        response = re.sub(r"(?<!\w)'|'(?!\w)", '"', response)

        # Ensure that keys are properly quoted
        response = re.sub(r'(\w+):\s*', r'"\1": ', response)

        # If a "reasoning" field exists, manually remove inner double quotes.
        reasoning_key = '"reasoning": '
        if reasoning_key in response:
            # Find the start of the reasoning value (the opening quote)
            key_index = response.find(reasoning_key)
            start_quote = response.find('"', key_index + len(reasoning_key))
            if start_quote != -1:
                # Assume the reasoning field is the last field in the object:
                end_quote = response.rfind('"')
                if end_quote > start_quote:
                    reasoning_value = response[start_quote+1:end_quote]
                    # Remove all inner double quotes from the reasoning content
                    fixed_reasoning = reasoning_value.replace('"', '')
                    response = response[:start_quote+1] + fixed_reasoning + response[end_quote:]

        # Remove quotes around timestamps (e.g., "00:00:06" becomes 00:00:06)
        response = re.sub(r'"(\d{2}:\d{2}:\d{2})"', r'\1', response)

        # Remove any trailing commas before closing braces or brackets
        response = re.sub(r",\s*([\]}])", r"\1", response)

        return json.loads(response)

    except json.JSONDecodeError as e:
        print(f"error:{e}")
        print(f"response:{response}")
        return {"error": "Invalid JSON response", "fixed_response": response}


def append_to_alerts_file(alert, file_path):
    """Append alert to the specified JSON file safely (using a temporary file)."""

    try:
        if not isinstance(alert, dict):
            raise ValueError("The 'alert' parameter must be a dictionary.")

        print(f"Appending alert: {alert}")
        data = []

        if os.path.exists(file_path):
            with open(file_path, 'r+') as f:
                try:
                    data = json.load(f)  # load existing data
                except json.JSONDecodeError as e:
                    print(f"JSONDecodeError: {e}. Corrupted file: {file_path}. Overwriting.")
                    data = []  # Start fresh if corrupted

                data.append(alert)
                temp_file_path = file_path + ".tmp"
                with open(temp_file_path, 'w') as temp_file:
                    json.dump(data, temp_file, indent=4)
                os.replace(temp_file_path, file_path)
                print(f"Alert appended to {file_path}. Entries: {len(data)}")
        else:
            with open(file_path, 'w') as f:
                json.dump([alert], f, indent=4)
                print(f"New file created; alert saved to {file_path}")

    except Exception as e:
        print(f"Failed to append to alerts file: {e}")
        print(f"Alert content that failed: {alert}")



#main pipeline
def run_pipeline(config_path,
                 video_path,
                 actions_path,
                 prompts_path,
                 context_path,
                 debug=False,
                 chunk_duration=30,
                 output_path=None):
    """
    Process a video in chunks and analyze each chunk using DeconstructorLean + Constructor.
    
    :param config_path: Path to pipeline configuration file.
    :param video_path: Path to input video.
    :param actions_path: Path to actions file.
    :param prompts_path: Path to prompts file.
    :param context_path: Path to context file.
    :param debug: Debug flag.
    :param chunk_duration: Duration (seconds) for each chunk.
    :param output_path: JSON file path to store alert results (e.g., suspicious chunks).
    """

    print(f"\n=== Starting Lean Pipeline for Stream: ===")

    # 1. Load configuration and data files
    config = load_yaml(config_path)
    actions = load_json(actions_path)
    prompts = load_json(prompts_path)


    #2. Initialize components
    deconstructor = DeconstructorFactory.create_deconstructor(video_path, config_path, None,
                                                              actions_path, prompts_path, context_path)
    
    constructor = Constructor(config=config)
    # Load the NX Witness server details from the config
    nx_server_url = config['stream']['server_url']
    nx_username = config['stream']['username']
    nx_password = config['stream']['password']

    # Initialize NX API and login
    nx_api = NxAPI()
    if not nx_api.login(nx_server_url, nx_username, nx_password):
        print("NX Witness login failed.")
        return

    # 3. Determine FPS from config
    fps = config['pipeline']['video']['fps']  #Watch out for KeyError if not in config
    vlm_provider = config['vlm']['provider']
    frames = load_video_frames(video_path, fps=fps)
    frames = standardize_frame(frames) # standerdizing frames to manage memory usage 
    total_frames = len(frames)
    print(f"Processing '{video_path}' at {fps} FPS, total frames: {total_frames}, chunk: {chunk_duration}s")
    #VLM provider selection
    if vlm_provider == "vllm":
        print("VLLM provider is selected")
        current_window_size = 5  
        start_index = 0
        vllm_results = {}
        total_frames = len(frames)

        def get_next_demo_alert(alerts_file="alerts.json"):
            try:
                with open(alerts_file, 'r') as f:
                    data = json.load(f)
                    sequence = data.get('demo_sequence', [])
                    current_index = data.get('current_index', 0)
                    
                    if current_index >= len(sequence):
                        return None
                        
                    stream_number = sequence[current_index]
                    
                    # Find the alert for this stream number
                    for alert in data['alerts']:
                        if alert['stream_number'] == stream_number:
                            # Update the current_index
                            data['current_index'] = current_index + 1
                            with open(alerts_file, 'w') as f:
                                json.dump(data, f, indent=4)
                            return alert
                    
                    return None
            except Exception as e:
                print(f"Error reading demo alerts: {e}")
                return None

        constructor_step_sec = 15
        constructor_step_frames = constructor_step_sec * fps
        last_constructor_call_frame = -constructor_step_frames

        while start_index < total_frames:
            end_index = start_index + current_window_size
            window_frames = frames[start_index:end_index]

            if not window_frames:
                break

            timestamp = format_timestamp((end_index - 1) // fps)
            current_prompt = deconstructor.get_current_prompt()
            pattern_name, instruction, match_found, vlm_output = deconstructor.compute_context(images=window_frames)
            print(f"VLLM Response for frames {start_index} to {end_index - 1}: {vlm_output}")

            if match_found:
                current_window_size = 10
            else:
                current_window_size = 5

            vllm_results[end_index - 1] = {
                "timestamp": timestamp,
                "pattern_name": pattern_name,
                "instruction": instruction,
                "window_size_used": current_window_size,
                "vlm_raw_response": f"{pattern_name}, {vlm_output}"
            }

            start_index = end_index

            if start_index >= 30 * fps and start_index >= last_constructor_call_frame + constructor_step_frames:
                print("Constructor call started")
                last_constructor_call_frame = start_index

                window_length_sec = 30
                step_sec = 15
                window_length_frames = window_length_sec * fps
                step_frames = step_sec * fps

                constructor_start_index = max(0, start_index - window_length_frames + step_frames)
                constructor_end_index = min(total_frames, constructor_start_index + window_length_frames)
                print(f"Constructor window: frames {constructor_start_index} to {constructor_end_index - 1}")

                chunk_actions = {}
                for frame_idx, result in vllm_results.items():
                    if constructor_start_index <= frame_idx < constructor_end_index:
                        chunk_actions[frame_idx] = result

                context_str = str(chunk_actions)
                try:
                    analysis = constructor.execute(
                        format=prompts['constructor']["constructor_prompt_output_format"],
                        context_q_and_a=context_str,
                        action_list="\n".join(actions["constructor"]),
                        constructor_header=prompts['constructor']["constructor_prompt_basic"]
                    )
                    
                    cleaned_chunk_analysis = clean_json_response(analysis.content)
                    if "error" in cleaned_chunk_analysis:
                        print(f"Invalid JSON response for constructor call: {cleaned_chunk_analysis['error']}")
                    
                    # Demo alert logic moved outside the error check
                    # Get the next demo alert after every constructor execution
                    demo_alert = get_next_demo_alert()
                    if demo_alert:
                        # Use the demo alert
                        chunk_result = {
                            "stream_number": demo_alert['stream_number'],
                            "chunk_start": demo_alert['chunk_start'],
                            "chunk_end": demo_alert['chunk_end'],
                            "analysis": demo_alert['analysis']
                        }
                        
                        # Always send to NX Witness
                        print(f"Sending NX event for demo alert from stream {demo_alert['stream_number']}")
                        nx_api.send_event(
                            "userdefinedevent",
                            "AI Security Alert",
                            f"Stream {demo_alert['stream_number']}: Suspicious Activity Detected: {demo_alert['analysis']['suspicious_activity']} "
                            f"(Level {demo_alert['analysis']['level_of_suspicion']})\n"
                            f"Reason: {demo_alert['analysis']['reasoning']}",
                            "Active"
                        )
                        
                        time.sleep(1)
                        nx_api.send_event(
                            "userdefinedevent",
                            "Reseting Event",
                            "restering",
                            "Inactive"
                        )
                        
                        # Append to alerts file
                        append_to_alerts_file(chunk_result, output_path)
                        print(f"Demo alert appended for stream {demo_alert['stream_number']}: {chunk_result}")
                    else:
                        print("No more demo alerts in sequence")
                    
                except Exception as e:
                    print(f"Error during Constructor/Demo execution for frames {constructor_start_index}-{constructor_end_index - 1}: {e}")
            
                  
    else:
        print("VLM provider is selected")
        for start_frame in range(0, total_frames, chunk_duration * fps):
            avg_time = 0  #to store average time of processing of each of frame
            end_frame = min(start_frame + (chunk_duration * fps), total_frames)
            chunk_frames = frames[start_frame:end_frame]

            print(f"Processing chunk: frames {start_frame}-{end_frame} (approx {chunk_duration}s)")
            chunk_actions = {}  # We'll store each frame analysis here


            # 4A. Frame-level processing
            # Add timing to frame-level processing
            for frame_number, frame in enumerate(chunk_frames, start=start_frame):
                try:
                    start_time = time.time()  # Start timing
                    timestamp = format_timestamp(frame_number // fps)
                    encoded_frame = encode_frame(frame, vlm_provider)
                    # print(type(encoded_frame))
                    # print(encoded_frame.mode)

                    # Deconstructor call
                    deconstructor.image = encoded_frame
                    pattern_name, instruction, match_found, vlm_output = deconstructor.compute_context() 

                    # Ensure valid values
                    pattern_name = pattern_name if pattern_name else "No Match"
                    instruction = instruction if instruction else "None"

                    # Now build a dictionary for chunk_actions
                    chunk_actions[frame_number] = {
                        "timestamp": timestamp,
                        "pattern_name": pattern_name,
                        "instruction": instruction
                    }

                    end_time = time.time()  # End timing
                    processing_time = end_time - start_time
                    avg_time =  (avg_time + processing_time) / 2
                    print(f"Frame {frame_number} processed in {processing_time:.4f} seconds.")

                except Exception as e:
                    import traceback
                    traceback.print_exc()  # This prints the full traceback
                    print(f"Error processing frame {frame_number}: {e}")
                    
                print(f"average time for processing each frame is:{avg_time}")


                # 4B. Analyze the chunk with the Constructor/LLM
                print(f"Calling Constructor/LLM for chunk {start_frame}-{end_frame}...")
            try:
                # Send chunk_actions as a string (or JSON) to the Constructor
                context_str = str(chunk_actions)  # or json.dumps(chunk_actions)
                analysis = constructor.execute(
                    format=prompts['constructor']["constructor_prompt_output_format"],
                    context_q_and_a=context_str,
                    action_list="\n".join(actions["constructor"]),
                    constructor_header=prompts['constructor']["constructor_prompt_basic"]
                )

                # Clean/validate the chunk analysis (this often returns JSON in .content)
                cleaned_chunk_analysis = clean_json_response(analysis.content)
                if "error" in cleaned_chunk_analysis:
                    print(f"Invalid JSON response for chunk {start_frame}-{end_frame}: {cleaned_chunk_analysis['error']}")
                    return

                chunk_result = {
                    "stream_number": video_path,  # or use extract_stream_name(video_path)
                    "chunk_start": format_timestamp(start_frame // fps),
                    "chunk_end": format_timestamp(end_frame // fps),
                    "analysis": cleaned_chunk_analysis
                }

                timestamps = []
                for frame_number, frame in enumerate(chunk_frames, start=start_frame):
                    timestamp = format_timestamp(frame_number // fps)
                    timestamps.append(timestamp)

                # Check suspicion level to decide whether to log
                suspicion_level = cleaned_chunk_analysis.get('level_of_suspicion', 0)
                if suspicion_level >= 3:
                    append_to_alerts_file(chunk_result, output_path)
                    print(f"Chunk result saved: {chunk_result}")
                else:
                    print(f"Chunk result skipped. Suspicion level: {suspicion_level}")
                
                # If constructor output indicates suspicious activity, send an NX event.
                if suspicion_level >= 3:
                        print("Sending NX event for suspicious activity.")
                        nx_api.send_event(
                            "userdefinedevent",
                            "AI Security Alert",
                            f"Suspicious Activity Detected: {chunk_result['analysis']['suspicious_activity']} "
                            f"(Level {chunk_result['analysis']['level_of_suspicion']})\n"
                            f"Reason: {chunk_result['analysis']['reasoning']}",
                            "Active"
                        )

                        time.sleep(1)
                        nx_api.send_event("userdefinedevent",
                                          "Reseting Event",
                                          " ",
                                          "Inactive")
            except Exception as e:
                print(f"Error during Constructor/LLM execution for chunk {start_frame}-{end_frame}: {e}")

    print(f"=== Pipeline Complete for Stream ===\n")
