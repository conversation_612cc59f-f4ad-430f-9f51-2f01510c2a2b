import os
import re
import time
import json
import base64
import cv2
import numpy as np
from PIL import Image
from io import BytesIO  
from datetime import timedelta
from src.constructor.constructor import Constructor
from src.deconstructor.deconstructor_factory import DeconstructorFactory
from src.utils.yaml_utils import load_yaml
from src.utils.helpers import load_json
from src.utils.video_utils import standardize_frame
from src.nx.nx_api import NxAPI
import time   # assume this is the file with your NxAPI class


def format_timestamp(seconds):
    """Format seconds into HH:MM:SS format."""
    return str(timedelta(seconds=seconds)).split('.')[0].zfill(8)

def encode_frame(frame, provider):
    """Encode frame based on provider."""
    if frame is None:
        raise ValueError("Invalid frame: Frame is None.")
    if isinstance(frame, str):
        raise ValueError(f"Unsupported frame type: {type(frame)}. Did you pass a file path instead of an image?")
    if isinstance(frame, Image.Image):
        pil_image = frame
    elif isinstance(frame, np.ndarray):
        pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
    else:
        raise ValueError(f"Unsupported frame type: {type(frame)}. Expected PIL Image or NumPy array.")

    if provider == "huggingface":
        return pil_image.convert("RGB")
    elif provider == "ollama":
        buffer = BytesIO()
        pil_image.save(buffer, format="JPEG")
        return base64.b64encode(buffer.getvalue()).decode('utf-8')
    elif provider == "vllm":
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        buffered = BytesIO()
        pil_image.save(buffered, format="JPEG", quality=85, optimize=True)
        del frame_rgb
        return base64.b64encode(buffered.getvalue()).decode()
    else:
        raise ValueError(f"Unsupported provider: {provider}. Use 'huggingface' or 'ollama'.")

def get_activity_threshold(cleaned_chunk_analysis, mapping_file_path="config/prompt/class_priority.json"):
    try:
        with open(mapping_file_path, 'r') as f:
            mapping = json.load(f)
    except Exception as e:
        print(f"Error loading mapping file: {e}")
        return None
    mapping_normalized = { key.lower(): value for key, value in mapping.items() }
    suspicious_activity_text = cleaned_chunk_analysis.get("suspicious_activity", "")
    normalized_activity = suspicious_activity_text.lower().strip()
    if normalized_activity.startswith("detected "):
        normalized_activity = normalized_activity[len("detected "):].strip()
    activity_keyword = normalized_activity.split(" - ")[0].strip()
    return mapping_normalized.get(activity_keyword)

def clean_json_response(response):
    try:
        # Remove Markdown-style JSON code blocks and trim whitespace
        response = re.sub(r'```json\n|\n```', '', response).strip()

        # Replace single quotes with double quotes for JSON standardization
        response = re.sub(r"(?<!\w)'|'(?!\w)", '"', response)

        # Ensure that keys are properly quoted
        response = re.sub(r'(\w+):\s*', r'"\1": ', response)

        # If a "reasoning" field exists, manually remove inner double quotes.
        reasoning_key = '"reasoning": '
        if reasoning_key in response:
            # Find the start of the reasoning value (the opening quote)
            key_index = response.find(reasoning_key)
            start_quote = response.find('"', key_index + len(reasoning_key))
            if start_quote != -1:
                # Assume the reasoning field is the last field in the object:
                end_quote = response.rfind('"')
                if end_quote > start_quote:
                    reasoning_value = response[start_quote+1:end_quote]
                    # Remove all inner double quotes from the reasoning content
                    fixed_reasoning = reasoning_value.replace('"', '')
                    response = response[:start_quote+1] + fixed_reasoning + response[end_quote:]

        # Remove quotes around timestamps (e.g., "00:00:06" becomes 00:00:06)
        response = re.sub(r'"(\d{2}:\d{2}:\d{2})"', r'\1', response)

        # Remove any trailing commas before closing braces or brackets
        response = re.sub(r",\s*([\]}])", r"\1", response)

        return json.loads(response)

    except json.JSONDecodeError as e:
        print(f"error:{e}")
        print(f"response:{response}")
        return {"error": "Invalid JSON response", "fixed_response": response}

def append_to_alerts_file(alert, file_path):
    try:
        if not isinstance(alert, dict):
            raise ValueError("The 'alert' parameter must be a dictionary.")
        print(f"Appending alert: {alert}")
        data = []
        if os.path.exists(file_path):
            with open(file_path, 'r+') as f:
                try:
                    data = json.load(f)
                except json.JSONDecodeError as e:
                    print(f"JSONDecodeError: {e}. Corrupted file: {file_path}. Overwriting.")
                    data = []
                data.append(alert)
                temp_file_path = file_path + ".tmp"
                with open(temp_file_path, 'w') as temp_file:
                    json.dump(data, temp_file, indent=4)
                os.replace(temp_file_path, file_path)
                print(f"Alert appended to {file_path}. Entries: {len(data)}")
        else:
            with open(file_path, 'w') as f:
                json.dump([alert], f, indent=4)
                print(f"New file created; alert saved to {file_path}")
    except Exception as e:
        print(f"Failed to append to alerts file: {e}")
        print(f"Alert content that failed: {alert}")

# ---------------------
# Stream Pipeline
# ---------------------
def run_stream_pipeline(config_path, stream_url, actions_path, prompts_path, context_path, output_path = None,):
    """
    Process an RTSP stream by capturing one frame per second, converting the stream to frames,
    and then processing frames using the existing sliding window logic.
    
    In this pipeline:
      - Frames are captured at 1 frame per second.
      - The VLLM sliding window logic is used (e.g., 5 frames initially, adjusting window size).
      - When a constructor call is triggered (every 30 seconds with a 15-second step), the aggregated window-level
        analysis is passed to the constructor.
      - If the constructor output indicates suspicious activity, an event is sent to NX Witness via the NX API.
      - When the stream ends, the NX session is deleted.
    """
    print(f"\n=== Starting Stream Pipeline: ===")
    config = load_yaml(config_path)
    actions = load_json(actions_path)
    prompts = load_json(prompts_path)
    
    # For stream processing, we assume 1 fps (set in config)
    fps = config['pipeline']['video']['fps']  # should be 1 for stream
    vlm_provider = config['vlm']['provider']
    nx_server_url =config['stream']['server_url']
    nx_username =config['stream']['username']
    nx_password =config['stream']['password']


    # Open the stream using OpenCV
    # cap = cv2.VideoCapture(stream_url)
    cap = cv2.VideoCapture(stream_url, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        print("Failed to open stream.")
        return

    # Initialize deconstructor and constructor
    deconstructor = DeconstructorFactory.create_deconstructor(stream_url, config_path, None, actions_path, prompts_path, context_path)
    constructor = Constructor(config=config)
    
    # Initialize NX API and login
    nx_api = NxAPI()
    if not nx_api.login(nx_server_url, nx_username, nx_password):
        print("NX Witness login failed.")
        return
    
    if vlm_provider == "vllm":
        # Initialize VLLM API
        print("VLLM provider is VLLM.")
        # Lists and counters for sliding window logic
        vllm_results = {}   # keyed by last frame index of each window
        frames_buffer = []  # accumulate frames as they arrive
        current_window_size = 5   # initial window size (in frames)
        start_index = 0           # pointer into frames_buffer
        frame_count = 0           # total frames captured
        
        # For controlling constructor call frequency
        constructor_step_sec = 15
        constructor_step_frames = constructor_step_sec * fps
        last_constructor_call_frame = -constructor_step_frames

        print(f"Processing stream from {stream_url} at {fps} fps.")
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("Stream ended or error reading frame.")
                    break

                # Since we want 1 frame per second, sleep for 1 sec between frames
                time.sleep(1)
                frame_count += 1
                # Standardize frame if necessary (assume standardize_frame returns a list)
                frame = standardize_frame([frame])[0]
                frames_buffer.append(frame)

                # When we have at least one window worth of frames, process a window
                if frame_count >= current_window_size:
                    window_frames = frames_buffer[start_index : start_index + current_window_size]
                    timestamp = format_timestamp((start_index + current_window_size - 1) // fps)
                    current_prompt = deconstructor.get_current_prompt()
                    # Process the window through VLLM compute_context
                    pattern_name, instruction, match_found, vlm_output = deconstructor.compute_context(images=window_frames)
                    print(f"VLLM Response for frames {start_index} to {start_index + current_window_size - 1}: {vlm_output}")
                    if match_found:
                        current_window_size = 10
                    else:
                        current_window_size = 5

                    # Store the window-level result (keyed by the last frame index of this window)
                    window_end_index = start_index + current_window_size - 1
                    vllm_results[window_end_index] = {
                        "timestamp": timestamp,
                        "pattern_name": pattern_name,
                        "instruction": instruction,
                        "window_size_used": current_window_size,
                        "vlm_raw_response": f"{pattern_name}, {vlm_output}"
                    }

                    # Move the window pointer forward
                    start_index += current_window_size

                    # Trigger a constructor call if we've processed at least 30 seconds and the step condition is met.
                    if start_index >= 30 * fps and start_index >= last_constructor_call_frame + constructor_step_frames:
                        print("Constructor call started")
                        last_constructor_call_frame = start_index
                        window_length_sec = 30  # 30-second window for constructor
                        window_length_frames = window_length_sec * fps
                        constructor_start_index = max(0, start_index - window_length_frames + constructor_step_frames)
                        constructor_end_index = min(len(frames_buffer), constructor_start_index + window_length_frames)
                        print(f"Constructor window: frames {constructor_start_index} to {constructor_end_index - 1}")

                        # Build aggregated analysis using already-computed window-level results
                        chunk_actions = {}
                        for frame_idx, result in vllm_results.items():
                            if constructor_start_index <= frame_idx < constructor_end_index:
                                chunk_actions[frame_idx] = result

                        context_str = str(chunk_actions)
                        try:
                            analysis = constructor.execute(
                                format=prompts['constructor']["constructor_prompt_output_format"],
                                context_q_and_a=context_str,
                                action_list="\n".join(actions["constructor"]),
                                constructor_header=prompts['constructor']["constructor_prompt_basic"]
                            )
                            cleaned_chunk_analysis = clean_json_response(analysis.content)
                            print(f"Constructor analysis: {cleaned_chunk_analysis}")
                            if "error" in cleaned_chunk_analysis:
                                print(f"Constructor error: {cleaned_chunk_analysis['error']}")
                            else:
                                chunk_result = {
                                    "stream_number": stream_url,
                                    "chunk_start": format_timestamp(constructor_start_index // fps),
                                    "chunk_end": format_timestamp((constructor_end_index - 1) // fps),
                                    "analysis": cleaned_chunk_analysis
                                }
                                # Determine threshold for suspicious activity
                                activity_threshold = get_activity_threshold(cleaned_chunk_analysis)
                                if activity_threshold is not None:
                                    current_level = cleaned_chunk_analysis.get("level_of_suspicion", 0)
                                    if current_level >= activity_threshold:
                                        append_to_alerts_file(chunk_result, output_path)
                                        print(f"Alert appended: {chunk_result}")
                                    else:
                                        print(f"Suspicion level {current_level} below threshold {activity_threshold}; alert not saved.")
                                else:
                                    print("No matching suspicious activity found in mapping.")
                                print(f"Constructor result for frames {constructor_start_index} to {constructor_end_index - 1}: {chunk_result}")
                            # If constructor output indicates suspicious activity, send an event via NX API.
                            if cleaned_chunk_analysis.get("is_suspicious", "").lower() == "yes":
                                print("Sending event to NX Witness.")
                                nx_api.send_event("userdefinedevent",
                                                "Suspicious Event",
                                                "Constructor flagged suspicious activity.",
                                                "Active")
                                time.sleep(5)  # Wait a few seconds before sending another event
                                nx_api.send_event("userdefinedevent",
                                                  "Suspicious Event",
                                                  "Constructor flagged suspicious activity.",
                                                  "Inactive")
                            print(f"Constructor result for frames {constructor_start_index} to {constructor_end_index - 1}: {cleaned_chunk_analysis}")
                        except Exception as e:
                            print(f"Error during Constructor execution for frames {constructor_start_index}-{constructor_end_index - 1}: {e}")

        except KeyboardInterrupt:
            print("Stream processing interrupted by user.")
        finally:
            cap.release()
            nx_api.delete_current_session()
            print("Stream processing complete.")
    else:
        # Non-VLLM provider: process each frame individually and store analysis results.
        print("Non-VLLM provider detected. Processing stream frames individually with pointer-based analysis aggregation.")
        analysis_results = {}  # Stores analysis for each frame keyed by frame number.
        frame_count = 0        # Global frame counter
        
        # Define sliding window parameters (in frames)
        window_size = 45  # initial window: 45 frames (i.e. 45 seconds at 1 fps)
        step = 15         # slide by 15 frames after each constructor call

        while True:
            ret, frame = cap.read()
            if not ret:
                print("Stream ended or error reading frame.")
                break

            time.sleep(1)  # Ensure ~1 fps
            frame_count += 1

            # Process the frame immediately using the deconstructor
            standardized_frame = standardize_frame([frame])[0]
            timestamp = format_timestamp(frame_count // fps)
            encoded = encode_frame(standardized_frame, vlm_provider)
            deconstructor.image = encoded

            # Compute context for this frame.
            # Use slicing to take only the first two values in case more are returned.
            result = deconstructor.compute_context()
            pattern_name, instruction = result[:2]
            pattern_name = pattern_name if pattern_name else "No Match"
            instruction = instruction if instruction else "None"
            print(f"Frame {frame_count} analysis: {pattern_name}, {instruction}")

            # Save the per-frame analysis.
            analysis_results[frame_count] = {
                "timestamp": timestamp,
                "pattern_name": pattern_name,
                "instruction": instruction
            }

            # When we've reached at least window_size frames and the frame_count is at a multiple of step,
            # call the constructor with the current window of analysis.
            if frame_count >= window_size and (frame_count - window_size) % step == 0:
                start_frame = frame_count - window_size + 1
                end_frame = frame_count
                # Extract the analysis for frames start_frame to end_frame.
                current_chunk = {k: analysis_results[k] for k in range(start_frame, end_frame + 1)}
                context_str = str(current_chunk)
                print(f"Calling Constructor for frames {start_frame} to {end_frame}...")
                try:
                    analysis = constructor.execute(
                        format=prompts['constructor']["constructor_prompt_output_format"],
                        context_q_and_a=context_str,
                        action_list="\n".join(actions["constructor"]),
                        constructor_header=prompts['constructor']["constructor_prompt_basic"]
                    )
                    cleaned_analysis = clean_json_response(analysis.content)
                    print(f"Constructor analysis for frames {start_frame}-{end_frame}: {cleaned_analysis}")
                    
                    # Check suspicion level and log an alert if needed.
                    suspicion_level = cleaned_analysis.get('level_of_suspicion', 0)
                    if suspicion_level >= 3:
                        chunk_result = {
                            "stream_number": stream_url,
                            "chunk_start": format_timestamp(start_frame // fps),
                            "chunk_end": format_timestamp(end_frame // fps),
                            "analysis": cleaned_analysis
                        }
                        append_to_alerts_file(chunk_result, output_path)
                        print(f"Alert appended for chunk {start_frame}-{end_frame}: {chunk_result}")
                    else:
                        print(f"Chunk analysis skipped; suspicion level: {suspicion_level}")
                        
                    # If constructor output indicates suspicious activity, send an NX event.
                    if cleaned_analysis.get("is_suspicious", "").lower() == "yes":
                        print("Sending NX event for suspicious activity.")
                        nx_api.send_event("userdefinedevent",
                                          "Suspicious Event",
                                          f"{cleaned_analysis}",
                                          "Active")
                        time.sleep(1)
                        nx_api.send_event("userdefinedevent",
                                          "Reseting Event",
                                          "restering",
                                          "Inactive")
                except Exception as e:
                    print(f"Error during Constructor execution for frames {start_frame}-{end_frame}: {e}")

        cap.release()
        nx_api.delete_current_session()
        print("Stream processing complete.")



    print(f"=== Pipeline Complete for Stream ===\n")

if __name__ == "__main__":
    # Example parameters (adjust paths and credentials as needed)
    config_path = "config/model/default.yaml"
    # Use your RTSP stream URL here
    stream_url = "rtsp://your_rtsp_source_url"
    actions_path = "config/prompt/actions.json"
    prompts_path = "config/prompt/prompts.json"
    context_path = "config/prompt/context.json"
    output_path = "alerts.json"
    # NX Witness server details
    nx_server_url = "https://your_nx_server_url"
    nx_username = "admin"
    nx_password = "your_password"

    run_stream_pipeline(config_path, stream_url, actions_path, prompts_path, context_path, output_path,
                        nx_server_url, nx_username, nx_password)
