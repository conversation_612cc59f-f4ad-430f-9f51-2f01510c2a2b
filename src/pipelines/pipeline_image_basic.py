import yaml
import base64
from src.constructor.constructor import Constructor
from src.deconstructor.deconstructor import Deconstructor
from src.utils.helpers import load_json, load_yaml

def load_config(config_path):
    with open(config_path, "r") as file:
        return yaml.safe_load(file)

def run_pipeline(config_path, image_path, actions_path, prompts_path, context_path):
    config = load_config(config_path)
    
    # Load prompts, actions, and context
    actions = load_json(actions_path)
    prompts = load_json(prompts_path)
    context = load_json(context_path)

    # Read and encode the image as bytes
    with open(image_path, "rb") as image_file:
        encoded_image = base64.b64encode(image_file.read()).decode('utf-8')

    # Execute Deconstructor
    deconstructor = Deconstructor(config_path, image_path, actions_path, prompts_path, context_path)
    scene_info, q_and_a = deconstructor.execute()
    print(scene_info, q_and_a)
    context_q_and_a = "Context:\n" + scene_info + "\n\n" + "Frame info:\n" + q_and_a   

    # Prepare constructor inputs
    action_list = "\n".join(actions["constructor"])
    constructor_header = prompts['constructor']["constructor_prompt_basic"] 
    format = prompts['constructor']["constructor_prompt_output_format"]

    # Execute Constructor
    constructor = Constructor(config=config)
    analysis = constructor.execute(format=format, context_q_and_a=context_q_and_a, action_list=action_list, constructor_header=constructor_header)
    print("Analysis:", analysis)
