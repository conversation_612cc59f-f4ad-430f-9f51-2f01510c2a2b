# import os
# import cv2
# import time
# import base64
# import yaml
# import json
# from datetime import timedelta
# from src.constructor.constructor import Constructor
# from src.deconstructor.deconstructor_factory import DeconstructorFactory
# from src.utils.helpers import load_json
# from src.utils.video_utils import load_video_frames
# from src.utils.yaml_utils import load_yaml

# def load_config(config_path):
#     with open(config_path, "r") as file:
#         return load_yaml(file)

# def encode_frame(frame):
#     _, buffer = cv2.imencode('.jpg', frame)
#     encoded_image = base64.b64encode(buffer).decode('utf-8')
#     return encoded_image

# def initialize_deconstructor(config_path, actions_path, prompts_path, context_path):
#     return DeconstructorFactory.create_deconstructor(
#         config_path, None, actions_path, prompts_path, context_path
#     )

# def compute_context(deconstructor, frame):
#     encoded_image = encode_frame(frame)
#     deconstructor.image_path = encoded_image
#     deconstructor.compute_context()
#     return deconstructor.scene_info

# def compute_top_questions(deconstructor, frame):
#     encoded_image = encode_frame(frame)
#     deconstructor.image_path = encoded_image
#     return deconstructor.compute_top_questions()

# def update_prompt(template, context, q_and_a, timestamp, frame_number):
#     formatted_context = template.format(timestamp=timestamp, frame_number=frame_number, context=context, q_and_a=q_and_a)
#     return formatted_context

# def format_timestamp(seconds):
#     return str(timedelta(seconds=seconds)).split('.')[0].zfill(8)

# def run_pipeline(config_path, video_path, actions_path, prompts_path, context_path, debug=False):
#     config = load_config(config_path)
#     actions = load_json(actions_path)
#     prompts = load_json(prompts_path)
#     context = load_json(context_path)
    
#     fps = config['pipeline']['video']['fps']
#     context_interval = config['pipeline']['video']['context_interval']
#     sliding_window_duration = config['pipeline']['video']['sliding_window_duration']
#     window_overlap = config['pipeline']['video']['window_overlap']

#     frames = load_video_frames(video_path, fps=fps)
#     total_frames = len(frames)
#     deconstructor = initialize_deconstructor(config_path, actions_path, prompts_path, context_path)
#     constructor = Constructor(config=config)

#     context_computed = False
#     scene_info = ""
#     all_responses = []
#     frame_matrix = {}  # Replace frame_data list with frame_matrix dictionary
#     frame_analysis = {}
#     full_prompt = prompts['constructor']["constructor_prompt_basic"] + "\n\n" + prompts['constructor']["constructor_prompt_ext"] + "\n\n"
#     scene_context_reasoning = "Scene info and Reasoning: " + "\n" + context['scene_info'] + "\n" + "\n".join(context['reasoning'])
#     full_prompt = full_prompt + scene_context_reasoning

#     for frame_number, frame in enumerate(frames):
#         seconds = frame_number // fps
#         timestamp = format_timestamp(seconds)

#         # Compute context periodically
#         if frame_number % (fps * context_interval) == 0 or not context_computed:
#             print(f"Computing context for frame {frame_number} [{timestamp}]")
#             scene_info = compute_context(deconstructor, frame)
#             if debug:
#                 # Image.fromarray(frame).show()
#                 cv2.imshow('Frame', frame)
#                 cv2.waitKey(1000)
#                 # Close the window
#                 cv2.destroyAllWindows()
#             full_prompt += update_prompt(prompts['constructor']["constructor_q_and_a_template"], scene_info, "", timestamp, frame_number)
#             context_computed = True

#         # Compute top questions for each frame
#         print(f"Computing top questions for frame {frame_number} [{timestamp}]")
#         context_q_and_a, top_q_and_a = compute_top_questions(deconstructor, frame)
        
#         # Process context Q&A
#         context_questions = []
#         context_answers = []
#         lines = context_q_and_a.split('\n')
#         for line in lines:
#             if 'Answer:' in line:
#                 question, answer = line.split('Answer:')
#                 context_questions.append(question.strip())
#                 context_answers.append(answer.strip())
        
#         # Process top Q&A
#         top_questions = []
#         top_answers = []
#         lines = top_q_and_a.split('\n')
#         for line in lines:
#             if 'Answer:' in line:
#                 question, answer = line.split('Answer:')
#                 top_questions.append(question.strip())
#                 top_answers.append(answer.strip())
        
#         # Create the JSON structures for context and top Q&As
#         context_q_and_a = {
#             "questions": context_questions,
#             "answers": context_answers
#         }
#         top_q_and_a = {
#             "questions": top_questions,
#             "answers": top_answers
#         }

#         # Create frame data and add it to frame_matrix
#         frame_info = {
#             "frame_number": frame_number,
#             "timestamp": timestamp,
#             "context_q_and_a": context_q_and_a,
#             "top_q_and_a": top_q_and_a
#         }
#         frame_analysis[frame_number] = frame_info
#         frame_matrix[frame_number] = frame
#         full_prompt += update_prompt(prompts['constructor']["constructor_q_and_a_template"], "", top_q_and_a, timestamp, frame_number)

#     # Call the constructor at the end of the video
#     action_list = "\n".join(actions["constructor"])
#     constructor_header = full_prompt
#     output_format = prompts['constructor']["constructor_prompt_output_format"]
#     analysis = constructor.execute(format=output_format, context_q_and_a="", action_list=action_list, constructor_header=constructor_header)
#     print(f"Final analysis: {analysis.content}")

#     all_responses.append(analysis)
#     return all_responses, frame_analysis, frame_matrix

# if __name__ == "__main__":
#     config_path = "config/model/default.yaml"
#     video_path = "path_to_your_video_file"
#     actions_path = "config/prompt/actions.json"
#     prompts_path = "config/prompt/prompts.json"
#     context_path = "config/prompt/context.json"

#     responses, frame_matrix = run_pipeline(config_path, video_path, actions_path, prompts_path, context_path)
#     print(f"Processed {len(frame_matrix)} frames")

#     # Optional: Save frame_matrix to a JSON file
#     with open("frame_matrix.json", "w") as f:
#         json.dump(frame_matrix, f, indent=2)










import os
import cv2
import time
import base64
import yaml
import json
from datetime import timedelta
from src.constructor.constructor import Constructor
from src.deconstructor.deconstructor_factory import DeconstructorFactory
from src.utils.helpers import load_json
from src.utils.video_utils import load_video_frames
from src.utils.yaml_utils import load_yaml


def load_config(config_path):
    with open(config_path, "r") as file:
        return load_yaml(file)


def encode_frame(frame):
    _, buffer = cv2.imencode('.jpg', frame)
    encoded_image = base64.b64encode(buffer).decode('utf-8')
    return encoded_image


def initialize_deconstructor(config_path, actions_path, prompts_path, context_path):
    return DeconstructorFactory.create_deconstructor(
        config_path, None, actions_path, prompts_path, context_path
    )


def compute_context(deconstructor, frame):
    encoded_image = encode_frame(frame)
    deconstructor.image_path = encoded_image
    deconstructor.compute_context()
    return deconstructor.scene_info


def append_to_json_file(data, file_path):
    """Appends data to a JSON file."""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r+') as file:
                existing_data = json.load(file)
                existing_data.append(data)
                file.seek(0)
                json.dump(existing_data, file, indent=4)
        else:
            with open(file_path, 'w') as file:
                json.dump([data], file, indent=4)
    except Exception as e:
        print(f"Error appending to JSON file: {e}")


def format_timestamp(seconds):
    return str(timedelta(seconds=seconds)).split('.')[0].zfill(8)


def run_pipeline(config_path, video_path, actions_path, prompts_path, context_path, output_path, chunk_duration=15, debug=False):
    config = load_config(config_path)
    actions = load_json(actions_path)
    prompts = load_json(prompts_path)
    context = load_json(context_path)

    fps = config['pipeline']['video']['fps']
    context_interval = config['pipeline']['video']['context_interval']

    frames = load_video_frames(video_path, fps=fps)
    total_frames = len(frames)

    deconstructor = initialize_deconstructor(config_path, actions_path, prompts_path, context_path)
    constructor = Constructor(config=config)

    full_prompt = prompts['constructor']["constructor_prompt_basic"] + "\n\n" + prompts['constructor']["constructor_prompt_ext"] + "\n\n"
    scene_context_reasoning = "Scene info and Reasoning: " + "\n" + context['scene_info'] + "\n" + "\n".join(context['reasoning'])
    full_prompt = full_prompt + scene_context_reasoning

    chunk_frames = []
    frame_analysis = {}
    for frame_number, frame in enumerate(frames):
        seconds = frame_number // fps
        timestamp = format_timestamp(seconds)

        # Compute context periodically
        if frame_number % (fps * context_interval) == 0:
            scene_info = compute_context(deconstructor, frame)
            full_prompt += prompts['constructor']["constructor_q_and_a_template"].format(
                timestamp=timestamp, frame_number=frame_number, context=scene_info, q_and_a=""
            )

        # Add frame to chunk
        chunk_frames.append((frame_number, frame))

        # Call Constructor after every chunk duration
        if seconds % chunk_duration == 0 and seconds != 0:
            chunk_analysis = []

            for frame_num, chunk_frame in chunk_frames:
                encoded_frame = encode_frame(chunk_frame)
                deconstructor.image_path = encoded_frame
                context_q_and_a = deconstructor.compute_top_questions()

                frame_info = {
                    "frame_number": frame_num,
                    "timestamp": format_timestamp(frame_num // fps),
                    "context_q_and_a": context_q_and_a
                }
                frame_analysis[frame_num] = frame_info

            # Analyze the chunk using Constructor
            action_list = "\n".join(actions["constructor"])
            constructor_header = full_prompt
            output_format = prompts['constructor']["constructor_prompt_output_format"]
            analysis = constructor.execute(format=output_format, context_q_and_a="", action_list=action_list,
                                            constructor_header=constructor_header)

            # Append chunk analysis to JSON file
            chunk_result = {
                "chunk_start": format_timestamp((frame_number - len(chunk_frames)) // fps),
                "chunk_end": timestamp,
                "analysis": analysis.content
            }
            append_to_json_file(chunk_result, output_path)

            # Clear the chunk frames for the next iteration
            chunk_frames = []

    print("Pipeline complete, results saved to:", output_path)

