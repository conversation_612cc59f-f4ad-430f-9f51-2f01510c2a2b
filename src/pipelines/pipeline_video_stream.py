import os
import cv2
import time
import base64
import yaml
import json
import re
from src.constructor.constructor import Constructor
from src.deconstructor.deconstructor import Deconstructor
from src.utils.helpers import load_json
from src.utils.stream import get_stream_capture, get_frame, load_stream_config
from http.server import <PERSON><PERSON><PERSON>erver, BaseHTTPRequestHandler
from threading import Thread
from src.utils.yaml_utils import load_yaml

import threading

class OutputHandler(BaseHTTPRequestHandler):
    latest_data = {'frame': None, 'analysis': None}
    data_lock = threading.Lock()

    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        with self.data_lock:
            self.wfile.write(json.dumps(self.latest_data).encode())

    @classmethod
    def set_data(cls, frame, analysis):
        with cls.data_lock:
            cls.latest_data = {
                'frame': frame.tolist() if frame is not None else None,
                'analysis': analysis.content if analysis else None
            }


def load_config(config_path):
    with open(config_path, "r") as file:
        return yaml.safe_load(file)


def encode_frame(frame):
    _, buffer = cv2.imencode('.jpg', frame)
    encoded_image = base64.b64encode(buffer).decode('utf-8')
    return encoded_image


def initialize_deconstructor(config_path, actions_path, prompts_path, context_path):
    return Deconstructor(config_path, None, actions_path, prompts_path, context_path)


def compute_context(deconstructor, frame):
    encoded_image = encode_frame(frame)
    deconstructor.image_path = encoded_image
    deconstructor.compute_context()
    return deconstructor.scene_info


def compute_top_questions(deconstructor, frame):
    encoded_image = encode_frame(frame)
    deconstructor.image_path = encoded_image
    return deconstructor.compute_top_questions()


def update_prompt(template, context, q_and_a, timestamp, frame_number):
    formatted_context = template.format(timestamp=timestamp, frame_number=frame_number, context=context,
                                        q_and_a=q_and_a)
    return formatted_context


def run_pipeline(config_path, actions_path, prompts_path, context_path, port=8000, debug=False):
    with open(config_path, 'r') as file:
        config = load_yaml(file)

    actions = load_json(actions_path)
    prompts = load_json(prompts_path)
    context = load_json(context_path)

    fps = config['pipeline']['video']['fps']
    context_interval = config['pipeline']['video']['context_interval']
    sliding_window_duration = config['pipeline']['video']['sliding_window_duration']
    window_overlap = config['pipeline']['video']['window_overlap']

    deconstructor = initialize_deconstructor(config_path, actions_path, prompts_path, context_path)
    constructor = Constructor(config=config)

    scene_info = ""
    all_responses = []
    all_frame_info = []
    full_prompt = prompts['constructor']["constructor_prompt_basic"] + "\n\n" + prompts['constructor']["constructor_prompt_ext"] + "\n\n"

    frames_per_window = int(fps * sliding_window_duration)
    frames_to_shift = int(fps * (sliding_window_duration - window_overlap))
    overlap_frames = frames_per_window - frames_to_shift

    cap = get_stream_capture(config_path)

    def run_server():
        server = HTTPServer(('localhost', port), OutputHandler)
        server.serve_forever()

    server_thread = Thread(target=run_server)
    server_thread.start()

    frame_count = 0

    try:
        while True:
            frame = get_frame(cap, fps)
            if frame is None:
                print("Failed to grab frame")
                break

            frame_count += 1

            seconds = frame_count // fps
            minutes = seconds // 60
            seconds = seconds % 60
            timestamp = f"{minutes} min, {seconds} sec"

            if frame_count % (fps * context_interval) == 0 or frame_count == 1:
                print(f"Computing context for frame {frame_count} [{timestamp}]")
                scene_info = compute_context(deconstructor, frame)

            print(f"Computing top questions for frame {frame_count} [{timestamp}]")
            _, top_q_and_a = compute_top_questions(deconstructor, frame)

            combined_context_and_qna = update_prompt(prompts['constructor']["constructor_q_and_a_template"], scene_info,
                                                     top_q_and_a, timestamp, frame_count)
            all_frame_info.append(combined_context_and_qna)

            analysis = None
            if frame_count % frames_to_shift == 0 and len(all_frame_info) >= frames_per_window:
                window_context_and_qna = all_frame_info[-frames_per_window:]

                action_list = "\n".join(actions["constructor"])
                constructor_header = full_prompt + "\n\n".join(window_context_and_qna)
                output_format = prompts['constructor']["constructor_prompt_output_format"]
                analysis = constructor.execute(format=output_format, context_q_and_a="", action_list=action_list,
                                               constructor_header=constructor_header)
                print(f" *** Analysis for window ending at frame {frame_count}: {analysis.content}")

                all_responses.append(analysis)

                # Keep overlap frames and remove the rest
                all_frame_info = all_frame_info[-overlap_frames:]

                OutputHandler.set_data(frame, analysis)
                print("posted results on localhost/port")
            else:
                OutputHandler.set_data(frame, analysis)
                print("posted results on localhost/port")

    except KeyboardInterrupt:
        print("Stream processing interrupted by user")
    finally:
        cap.release()

    # Process remaining frames
    if len(all_frame_info) > 0:
        window_context_and_qna = all_frame_info[-frames_per_window:] if len(all_frame_info) >= frames_per_window else all_frame_info
        action_list = "\n".join(actions["constructor"])
        constructor_header = full_prompt + "\n\n".join(window_context_and_qna)
        output_format = prompts['constructor']["constructor_prompt_output_format"]
        analysis = constructor.execute(format=output_format, context_q_and_a="", action_list=action_list,
                                       constructor_header=constructor_header)
        print(f" *** Final analysis for remaining frames: {analysis.content}")
        all_responses.append(analysis)

    return all_responses


if __name__ == "__main__":
    config_path = "config/model/default.yaml"
    video_path = "path_to_your_video_file"
    actions_path = "config/prompt/actions.json"
    prompts_path = "config/prompt/prompts.json"
    context_path = "config/prompt/context.json"

    run_pipeline(config_path, video_path, actions_path, prompts_path, context_path)
