import importlib

class VectorStoreFactory:
    @staticmethod
    def create_vector_store(config):
        provider = config['vector_store']['provider']
        module_name = f"src.vector_stores.{provider}_store"
        class_name = provider.capitalize() + "Store"
        
        try:
            module = importlib.import_module(module_name)
            StoreClass = getattr(module, class_name)
            print(f"Config path: {config['vector_store']['path']}, Config dim: {config['vector_store']['dim']}")
            return StoreClass(dim=config['vector_store']['dim'], index_path=config['vector_store']['path'])
        except (ModuleNotFoundError, AttributeError) as e:
            raise ValueError(f"Error loading vector store provider {provider}: {e}")
