import faiss
import numpy as np
import os
import hashlib
from .base_vector_store import BaseVectorStore
from scripts.vectorize_data import vectorize_data

class FaissStore(BaseVectorStore):
    def __init__(self, dim, index_path):
        self.dim = dim
        self.index_path = index_path
        self.existing_vector_hashes = set()

        # Ensure the directory exists
        directory = os.path.dirname(index_path)
        if not os.path.exists(directory):
            os.makedirs(directory)

        if os.path.exists(index_path):
            try:
                self.index = faiss.read_index(index_path)
                print(f"Index loaded from {index_path}")
                self._load_existing_hashes()
            except RuntimeError as e:
                print(f"Error reading index file: {e}. Creating a new index.")
                self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
        else:
            print(f"Index file does not exist. Create a vector store.")
            self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))

    def _load_existing_hashes(self):
        hash_path = f"{self.index_path}_hashes.txt"
        if os.path.exists(hash_path):
            with open(hash_path, 'r') as f:
                self.existing_vector_hashes = set(line.strip() for line in f)
            print(f"Loaded {len(self.existing_vector_hashes)} existing vector hashes.")

    def _save_hashes(self):
        hash_path = f"{self.index_path}_hashes.txt"
        with open(hash_path, 'w') as f:
            for hash_str in self.existing_vector_hashes:
                f.write(hash_str + '\n')
        print(f"Saved {len(self.existing_vector_hashes)} vector hashes.")

    def _hash_vector(self, vector):
        return hashlib.md5(vector.tobytes()).hexdigest()

    def add_vectors(self, vectors, ids):
        vectors_to_add = []
        ids_to_add = []
        for vector, id_ in zip(vectors, ids):
            hash_str = self._hash_vector(vector)
            if hash_str not in self.existing_vector_hashes:
                vectors_to_add.append(vector)
                ids_to_add.append(id_)
                self.existing_vector_hashes.add(hash_str)

        if vectors_to_add:
            vectors_to_add = np.array(vectors_to_add).astype('float32')
            ids_to_add = np.array(ids_to_add).astype('int64')
            assert vectors_to_add.shape[1] == self.dim, "Dimensionality mismatch between vectors and FAISS index."
            self.index.add_with_ids(vectors_to_add, ids_to_add)
            print(f"Added {len(vectors_to_add)} vectors to the index.")
            self._save_hashes()
        else:
            print("No new vectors to add.")

    def save(self):
        try:
            faiss.write_index(self.index, self.index_path)
            print(f"Index saved at {self.index_path}")
            self._save_hashes()
        except Exception as e:
            print(f"Error writing index file: {e}")

    def search(self, vector, top_k=5):
        distances, indices = self.index.search(np.array([vector]).astype('float32'), top_k)
        return indices[0], distances[0]

    def embedding_exists(self, embedding):
        hash_str = self._hash_vector(np.array(embedding).astype('float32'))
        return hash_str in self.existing_vector_hashes
