import os
import torch
import numpy as np
from transformers import AutoConfig, AutoModelForCausalLM
from janus.models.processing_vlm import VLChatProcessor
from PIL import Image
import time

def preprocess_image(image):
    # Ensure image is in RGB format
    if image.mode != 'RGB':
        image = image.convert('RGB')
        print(image)
    return image

def load_model():
    model_name = "deepseek-ai/Janus-Pro-1B"
    
    # Load config and model
    config = AutoConfig.from_pretrained(model_name)
    language_config = config.language_config
    language_config._attn_implementation = "eager"

    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        language_config=language_config,
        trust_remote_code=True
    )

    # Load processor
    processor = VLChatProcessor.from_pretrained(model_name)

    # Move model to appropriate device
    device = "cuda" if torch.cuda.is_available() else "cpu"
    if torch.cuda.is_available():
        model = model.to(torch.bfloat16).cuda()
    else:
        model = model.to(torch.float16)

    return model, processor, device

def generate_response(image, question):
    
    tokenizer = processor.tokenizer
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    processed_image = preprocess_image(image)
    
    conversation = [
        {
            "role": "<|User|>",
            "content": f"<image_placeholder>\nDescribe this image in 2-3 words only.",
            "images": [processed_image],
        },
        {"role": "<|Assistant|>", "content": ""},
    ]

    # Prepare inputs
    prepare_inputs = processor(
        conversations=conversation, 
        images=[processed_image], 
        force_batchify=True
    ).to(device, dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float16)

    inputs_embeds = model.prepare_inputs_embeds(**prepare_inputs)

    # Generate response with fewer tokens
    with torch.inference_mode():
        outputs = model.language_model.generate(
            inputs_embeds=inputs_embeds,
            attention_mask=prepare_inputs.attention_mask,
            pad_token_id=tokenizer.eos_token_id,
            bos_token_id=tokenizer.bos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            max_new_tokens=32,  # Reduced from 512
            do_sample=True,
            temperature=0.7,
            top_p=0.95,
        )

    response = tokenizer.decode(outputs[0].cpu().tolist(), skip_special_tokens=True)
    return response

