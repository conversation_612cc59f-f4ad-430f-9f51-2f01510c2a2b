import base64
from io import BytesIO
from PIL import Image
import numpy as np
import torch
from transformers import AutoConfig, AutoModelForCausalLM
from janus.models.processing_vlm import VLChatProcessor
from .vlm_interaction import BaseVLM

class HuggingfaceVLM(BaseVLM):
    def __init__(self, model="deepseek-ai/Janus-Pro-1B"):
        """
        Initialize the HuggingfaceVLM with the specified model.
        """
        self.model_name = model
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = None
        self.processor = None
        self.load_model()

    def load_model(self):
        """
        Load and configure the model and processor.
        """
        print(f"[HuggingfaceVLM] Loading model: {self.model_name} on {self.device}")
        
        # Load configuration and tweak attention implementation for stability/performance.
        config = AutoConfig.from_pretrained(self.model_name)
        language_config = config.language_config
        language_config._attn_implementation = "eager"
        
        # Load the causal language model with the custom language configuration.
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            language_config=language_config,
            trust_remote_code=True
        )
        
        # Choose the appropriate data type based on the device.
        dtype = torch.bfloat16 if torch.cuda.is_available() else torch.float16
        self.model = self.model.to(dtype).to(self.device)
        
        # Load the vision-language processor.
        self.processor = VLChatProcessor.from_pretrained(self.model_name)
        print("[HuggingfaceVLM] Model and processor loaded successfully.")

    def preprocess_image(self, frame):
        """
        Preprocess an image input to ensure it is in RGB format.
        Supports inputs as PIL.Image, bytes, or numpy.ndarray.
        """
        if isinstance(frame, Image.Image):
            image = frame.convert("RGB")
        elif isinstance(frame, bytes):
            image = Image.open(BytesIO(frame)).convert("RGB")
        elif isinstance(frame, np.ndarray):
            image = Image.fromarray(frame).convert("RGB")
        else:
            raise ValueError(f"Unsupported frame type: {type(frame)}. Expected bytes, np.ndarray, or PIL.Image.")
        return image

    def predict(self, inputs):
        """
        Generate predictions for the provided images and text prompt.
        
        Args:
            inputs: A tuple (frames, questions) where:
                - frames: List of image inputs.
                - questions: List of corresponding text prompts.
        
        Returns:
            A list of strings representing the generated responses.
        """
        frames, questions = inputs
        # Process all frames
        processed_images = []
        for frame in frames:
            try:
                processed_image = self.preprocess_image(frame)
                processed_images.append(processed_image)
            except Exception as e:
                print(f"[Warning] Error processing frame: {e}")
        
        if not processed_images:
            raise RuntimeError("No valid frames were processed")

        # Build the conversation structure.
        # Here, we use a simple prompt instruction along with the image placeholder.
        prompt = "Describe this image in two words restrictly"
        conversation = [
            {
                "role": "<|User|>",
                "content": f"<image_placeholder>\n{prompt}",
                "images": processed_images,
            },
            {"role": "<|Assistant|>", "content": ""},
        ]
        
        # Prepare inputs (tokenization and image processing) for the model.
        prepare_inputs = self.processor(
            conversations=conversation, 
            images=processed_images, 
            force_batchify=True
        ).to(self.device, dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float16)
        
        # Convert prepared inputs to embeddings.
        inputs_embeds = self.model.prepare_inputs_embeds(**prepare_inputs)
        
        # Define generation parameters.
        generate_kwargs = {
            "inputs_embeds": inputs_embeds,
            "attention_mask": prepare_inputs.attention_mask,
            "pad_token_id": self.processor.tokenizer.eos_token_id,
            "bos_token_id": self.processor.tokenizer.bos_token_id,
            "eos_token_id": self.processor.tokenizer.eos_token_id,
            "max_new_tokens": 32,  # You can adjust this value as needed.
            "do_sample": True,
            "temperature": 0.7,
            "top_p": 0.95,
        }
        
        # Generate response with torch's inference mode (no gradients).
        with torch.inference_mode():
            outputs = self.model.language_model.generate(**generate_kwargs)
        
        # Decode the generated tokens into a human-readable string.
        response = self.processor.tokenizer.decode(outputs[0].cpu().tolist(), skip_special_tokens=True)
        
        # Return the response, optionally splitting it into lines.
        return response.strip().split("\n")
