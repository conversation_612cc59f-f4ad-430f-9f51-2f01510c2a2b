import importlib

class VLMFactory:
    @staticmethod
    def create_vlm(config):
        provider = config['vlm']['provider']
        module_name = f"src.vlm.{provider}_vlm"
        class_name = provider.capitalize() + "VLM"
        
        try:
            module = importlib.import_module(module_name)
            VLMClass = getattr(module, class_name)
            return VLMClass(model=config['vlm']['model_name'])
        except (ModuleNotFoundError, AttributeError) as e:
            raise ValueError(f"Error loading VLM provider {provider}: {e}")
