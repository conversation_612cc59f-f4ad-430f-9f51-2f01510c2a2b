import base64
import ollama
from .vlm_interaction import BaseVLM

class OllamaVLM(BaseVLM):
    def __init__(self, model="phi4"):
        self.model = model
        self.load_model()

    def load_model(self):
        # Load model if needed
        pass

    def encode_image(self, image_path):
        with open(image_path, "rb") as image_file:
            encoded_image = base64.b64encode(image_file.read()).decode('utf-8')
        return encoded_image

    def predict(self, inputs):
        encoded_image, questions = inputs
        prompt = "\n\n".join(questions)
        response_text = ""
        for response in ollama.generate(
            model=self.model,
            prompt=prompt,
            images=[encoded_image],
            stream=True
        ):
            response_text += response['response']
        return response_text.strip().split('\n')
