from vllm import LLM, SamplingParams
from .vlm_interaction import BaseVLM
from PIL import Image
import numpy as np
from src.pipelines.pipeline_video_basic_lean import encode_frame
from typing import List, Dict, Any

class VllmVLM(BaseVLM):
    def __init__(self, model=""):
        self.model = model
        self._sampling_params = None
        self.load_model()
    
    def load_model(self):
        self.llm = LLM(self.model,
                       trust_remote_code=True,
                       limit_mm_per_prompt={"image": 10},)
        
        print("[VLLM] Model loaded successfully.")

    def create_message(self, frames: List[np.ndarray], instruction: str) -> Dict:
        """Create a message with frames and instruction for the model."""
        message = {
            "role": "user",
            "content": [{"type": "text", "text": instruction}]
        }
        
        for frame in frames:
            base64_image = encode_frame(frame,"vllm")
            message["content"].append({
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}
            })
        
        return message

    
    def sampling_params(self) -> SamplingParams:
        """Get sampling parameters for controlled output."""
        if self._sampling_params is None:
            self._sampling_params = SamplingParams(
                max_tokens=128,
                temperature=0.7,
                top_p=0.9,
                stop=["</s>", "Human:", "Assistant:"]
            )
        return self._sampling_params

    def predict(self, message: Dict[str, Any]) -> str:
        """Make a prediction using the VLM."""
        outputs = self.llm.chat(
            messages=[message],
            sampling_params=self.sampling_params()
        )
        
        return outputs[0].outputs[0].text if outputs else ""