import requests
import json
import time 

class NxAPI:
    def __init__(self):
        self.api = None
        self.session = requests.Session()
        # Disable SSL verification (like in C++ code using OpenSSL with certificate verification disabled)
        self.session.verify = False
        self.headers = {"Content-Type": "application/json"}
        self.rtsp_devices = []  # To store RTSP devices from the server

    def login(self, api_url, username, password):
        """
        Logs in to the NX Witness server.
        This sends a JSON payload to /rest/v3/login/sessions and on success extracts the token.
        It then fetches devices and stores any with 'GENERIC_RTSP-stream' in their name.
        """
        self.api = api_url
        login_url = f"{self.api}/rest/v3/login/sessions"
        payload = {
            "username": username,
            "password": password,
            "setCookie": True
        }
        
        response = self.session.post(login_url, headers=self.headers, json=payload)
        if response and response.status_code == 200:
            print("Login successful.")
            data = response.json()
            token = data.get("token")
            if token:
                self.headers["Authorization"] = f"Bearer {token}"
            else:
                print("Error: No token received.")
                return False

            # Fetch devices to store RTSP devices for event management
            devices_url = f"{self.api}/rest/v3/devices"
            response = self.session.get(devices_url, headers=self.headers)
            if response and response.status_code == 200:
                devices = response.json()
                for device in devices:
                    name = device.get("name", "")
                    if "GENERIC_RTSP-stream" in name:
                        self.rtsp_devices.append((name, device.get("id")))
                return True
            else:
                print("Failed to get devices. Status:", response.status_code)
                return False
        else:
            print("Login failed. Status:", response.status_code if response else "no response")
            print("Response:", response.text if response else "no response")
            return False

    def send_event(self, event_type, caption, description, state):
        """
        Sends an event to the NX Witness server.
        The event JSON includes a type, caption, description, state, and metadata.
        """
        url = f"{self.api}/api/createEvent"
        event_data = {
            "type": event_type,
            "caption": caption,
            "description": description,
            "state": state,
            "metadata": {
                "cameraRefs": ["d10a4621-e337-59d4-8629-7a0a460b32e6"]
            }
        }
        response = self.session.post(url, headers=self.headers, json=event_data)
        if response and response.status_code == 200:
            print("Event created successfully.")
            return True
        else:
            print("Failed to create event. Status:", response.status_code if response else "no response")
            print("Response:", response.text if response else "no response")
            return False

    def delete_current_session(self):
        """
        Deletes the current session (logs out) from the NX Witness server.
        """
        url = f"{self.api}/rest/v3/login/sessions/current"
        response = self.session.delete(url, headers=self.headers)
        if response and response.status_code == 200:
            print("Current session deleted successfully.")
            return True
        else:
            print("Failed to delete session. Status:", response.status_code if response else "no response")
            print("Response:", response.text if response else "no response")
            return False

# Example usage:
if __name__ == "__main__":
    # Replace with your actual NX Witness server URL, username, and password.
    nx_api = NxAPI()
    # Example URL: "https://**************:7001"
    if nx_api.login("https://**************:7001", "admin", "Deepedge@2118"):
        # Optionally print out discovered RTSP devices
        for name, dev_id in nx_api.rtsp_devices:
            print(f"Device Name: {name}, Device ID: {dev_id}")
        
        # Send an event (for example, when an alert is triggered in your pipeline)
        nx_api.send_event("userdefinedevent", "Test Caption", """{
        "stream_number": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_1_videos.mp4",
        "chunk_start": "00:06:15",
        "chunk_end": "00:06:44",
        "analysis": {
            "is_suspicious": "Yes",
            "suspicious_activity": "Detected shoplifting",
            "level_of_suspicion": 4,
            "reasoning": "The VLM outputs indicate 'shoplifting' and 'tampering with cash register', which suggest a retail theft scenario. Although there's a 'No Match' pattern, the mentioned activities align with unauthorized, intentful actions associated with shoplifting and manipulation of point-of-sale devices. This raises a strong suspicion of shoplifting activity."
        }
    }""", "Active")
        time.sleep(1)
        nx_api.send_event("userdefinedevent", "Test Caption", """{
        "stream_number": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_1_videos.mp4",
        "chunk_start": "00:06:15",
        "chunk_end": "00:06:44",
        "analysis": {
            "is_suspicious": "Yes",
            "suspicious_activity": "Detected shoplifting",
            "level_of_suspicion": 4,
            "reasoning": "The VLM outputs indicate 'shoplifting' and 'tampering with cash register', which suggest a retail theft scenario. Although there's a 'No Match' pattern, the mentioned activities align with unauthorized, intentful actions associated with shoplifting and manipulation of point-of-sale devices. This raises a strong suspicion of shoplifting activity."
        }
    }""", "Inactive")
        
        # Delete the session when finished (log out)
        nx_api.delete_current_session()




