import cv2
import time
import yaml


def load_stream_config(config_path):
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    return config['stream']


def create_rtsp_url(stream_config):
    return f"rtsp://{stream_config['username']}:{stream_config['password']}@{stream_config['ip_address']}:{stream_config['port']}/cam/realmonitor?channel={stream_config['channel']}&subtype={stream_config['subtype']}"


def get_stream_capture(config_path):
    stream_config = load_stream_config(config_path)
    rtsp_url = create_rtsp_url(stream_config)
    cap = cv2.VideoCapture(rtsp_url)
    if not cap.isOpened():
        raise ValueError("Failed to open RTSP stream. Please check the URL and your network connection.")
    return cap


def get_frame(cap, fps):
    start_time = time.time()
    while True:
        ret, frame = cap.read()
        if not ret:
            return None

        current_time = time.time()
        elapsed_time = current_time - start_time

        if elapsed_time >= (1 / fps):
            return frame