import cv2
import os
import numpy as np
import base64
import tiktoken
from openai import OpenAI

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************"))
from PIL import Image
from io import BytesIO

# Set your API key here or use the OPENAI_API_KEY environment variable.

def load_video_frames(video_path, fps=1):
    cap = cv2.VideoCapture(video_path)
    frames = []
    frame_rate = cap.get(cv2.CAP_PROP_FPS)
    frame_interval = int(frame_rate // fps) if fps > 0 else 1
    frame_count = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(Image.fromarray(frame_rgb))
        frame_count += 1
    cap.release()
    return frames

def standardize_frame(frames, target_size=512):
    std_frames = []
    for frame in frames:
        if hasattr(frame, "convert"):
            frame = np.array(frame)
        h, w = frame.shape[:2]
        if h > target_size or w > target_size:
            if h > w:
                new_h = target_size
                new_w = int(w * target_size / h)
            else:
                new_w = target_size
                new_h = int(h * target_size / w)
            frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
        std_frames.append(frame)
    return std_frames

def save_frame(frame, output_path, frame_number):
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    frame_path = os.path.join(output_path, f"frame_{frame_number}.jpg")
    cv2.imwrite(frame_path, frame)
    return frame_path

def get_frame_at_time(video_path, time_sec):
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_MSEC, time_sec * 1000)
    ret, frame = cap.read()
    cap.release()
    if not ret:
        return None
    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    return Image.fromarray(frame_rgb)

def pil_to_base64(image, fmt="JPEG"):
    buffered = BytesIO()
    image.save(buffered, format=fmt)
    return "data:image/jpeg;base64," + base64.b64encode(buffered.getvalue()).decode("utf-8")

def call_gpt4_vision(prompt, images_b64):
    messages = [{
        "role": "user",
        "content": [{"type": "text", "text": prompt}] +
                   [{"type": "image_url", "image_url": {"url": b64}} for b64 in images_b64]
    }]
    response = client.chat.completions.create(model="gpt-4o",  # Use the new model name per updated API
    messages=messages)
    return response.choices[0].message.content

def token_count_multi(input_data, output_text, model="gpt-4o", detail="high"):
    encoding = tiktoken.encoding_for_model(model)
    def count_text(text):
        return len(encoding.encode(text))
    def count_image(img):
        if isinstance(img, str) and img.startswith("data:image/"):
            _, encoded = img.split(",", 1)
            img = Image.open(BytesIO(base64.b64decode(encoded)))
        if not isinstance(img, Image.Image):
            raise ValueError("Unsupported image type.")
        return 85 if detail.lower() == "low" else 85 + 170 if detail.lower() == "high" else 0
    def process(item):
        if isinstance(item, str):
            return count_image(item) if item.startswith("data:image/") else count_text(item)
        elif isinstance(item, Image.Image):
            return count_image(item)
        else:
            raise ValueError("Unsupported input type.")
    tokens_in = sum(process(x) for x in input_data) if isinstance(input_data, list) else process(input_data)
    tokens_out = count_text(output_text)
    return {"input_tokens": tokens_in, "output_tokens": tokens_out}

def parse_time_str(time_str):
    mins, secs = time_str.split(":")
    return int(mins) * 60 + int(secs)
