import cv2
import os
from PIL import Image
import numpy as np

def load_video_frames(video_path, fps=3):
    cap = cv2.VideoCapture(video_path)
    frames = []
    frame_rate = cap.get(cv2.CAP_PROP_FPS)
    frame_interval = int(frame_rate // fps)

    frame_count = 0
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            frames.append(pil_image)
        frame_count += 1

    cap.release()
    return frames

def standardize_frame(frames, target_size: int = 512):
    """Standardize frame size to manage memory usage.

    Args:
        frames: List of frames (PIL Images or NumPy arrays)
        target_size: Target size for the longer dimension

    Returns:
        List of resized frames (as NumPy arrays) maintaining aspect ratio
    """
    if frames is None:
        return None

    standardized_frames = []
    for frame in frames:
        # If the frame is a PIL Image, convert it to a NumPy array.
        if hasattr(frame, "convert"):  # simple check for PIL image
            frame = np.array(frame)
        
        h, w = frame.shape[:2]
        if h > target_size or w > target_size:
            # Calculate new dimensions while maintaining the aspect ratio
            if h > w:
                new_h = target_size
                new_w = int(w * target_size / h)
            else:
                new_w = target_size
                new_h = int(h * target_size / w)
            
            # Resize using area interpolation for better quality
            frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        standardized_frames.append(frame)
    
    return standardized_frames

def save_frame(frame, output_path, frame_number):
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    frame_path = os.path.join(output_path, f"frame_{frame_number}.jpg")
    cv2.imwrite(frame_path, frame)
    return frame_path
