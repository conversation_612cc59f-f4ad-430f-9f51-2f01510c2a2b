from prometheus_client import start_http_server, Gauge, Counter
import time
import psutil
import pynvml
import functools
import threading

# Initialize NVML for GPU metrics
pynvml.nvmlInit()

# Define Prometheus metrics
INFERENCE_TIME = Gauge('inference_total_time_seconds', 'Total inference time in seconds')
TIME_TO_FIRST_TOKEN = Gauge('time_to_first_token_seconds', 'Time to first token in seconds')
TOKENS_PER_SECOND = Gauge('tokens_per_second', 'Tokens per second')
TOKEN_COUNT = Counter('token_count_total', 'Total number of tokens processed')

# System utilization metrics
CPU_UTIL = Gauge('cpu_utilization_percent', 'CPU utilization percentage')
RAM_UTIL = Gauge('ram_utilization_percent', 'RAM utilization percentage')
GPU_UTIL = Gauge('gpu_utilization_percent', 'GPU utilization percentage')
GPU_MEM_UTIL = Gauge('gpu_memory_utilization_percent', 'GPU memory utilization percentage')

def get_system_utilization():
    """Fetch system resource usage"""
    cpu = psutil.cpu_percent(interval=1)
    mem = psutil.virtual_memory().percent
    try:
        handle = pynvml.nvmlDeviceGetHandleByIndex(0)
        gpu_info = pynvml.nvmlDeviceGetUtilizationRates(handle)
        gpu = gpu_info.gpu
        gpu_mem_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
        gpu_mem = (gpu_mem_info.used / gpu_mem_info.total) * 100
    except Exception as e:
        gpu = 0
        gpu_mem = 0
    return cpu, mem, gpu, gpu_mem

def update_system_utilization():
    """Continuously update system utilization metrics"""
    while True:
        cpu, mem, gpu, gpu_mem = get_system_utilization()
        CPU_UTIL.set(cpu)
        RAM_UTIL.set(mem)
        GPU_UTIL.set(gpu)
        GPU_MEM_UTIL.set(gpu_mem)
        time.sleep(1)  # Update every 1 second

def inference_metrics(func):
    """
    Decorator to collect inference performance metrics.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        total_time = time.perf_counter() - start_time

        # If the function returns a tuple, join its elements into a string
        result_str = " ".join(str(x) for x in result) if isinstance(result, tuple) else str(result)

        tokens = result_str.split()
        token_count = len(tokens)
        tps = token_count / total_time if total_time > 0 else 0
        first_token_time = (total_time / token_count) if token_count > 0 else total_time

        INFERENCE_TIME.set(total_time)
        TIME_TO_FIRST_TOKEN.set(first_token_time)
        TOKENS_PER_SECOND.set(tps)
        TOKEN_COUNT.inc(token_count)

        return result
    return wrapper

# Start Prometheus metrics server (direct scraping)
METRICS_PORT = 8000
METRICS_IP = "**************"
start_http_server(METRICS_PORT, addr=METRICS_IP)
print(f"✅ Prometheus metrics endpoint running at http://{METRICS_IP}:{METRICS_PORT}/metrics")

# Start system utilization monitoring in the background
monitoring_thread = threading.Thread(target=update_system_utilization, daemon=True)
monitoring_thread.start()
