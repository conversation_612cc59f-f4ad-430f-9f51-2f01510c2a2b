import yaml
from typing import Dict, Any, Union
from io import TextIOWrapper


def load_yaml(file_input: Union[str, TextIOWrapper]) -> Dict[str, Any]:
    """
    Load a YAML file or file object, replace placeholders with local variable values, and return the processed data.

    Placeholders in the YAML file should be in the format {variable_name}.
    The function will look for any variables defined at the top level of the YAML
    and use their values to replace corresponding placeholders in other strings.

    Args:
    file_input (Union[str, TextIOWrapper]): Path to the YAML file or an opened file object.

    Returns:
    Dict[str, Any]: Processed YAML data with placeholders replaced.

    Raises:
    yaml.YAMLError: If there's an error parsing the YAML file.
    """

    def find_local_vars(data: Dict[str, Any]) -> Dict[str, str]:
        return {k: v for k, v in data.items() if isinstance(v, str)}

    def replace_placeholders(data: Any, replacements: Dict[str, str]) -> Any:
        if isinstance(data, dict):
            return {k: replace_placeholders(v, replacements) for k, v in data.items()}
        elif isinstance(data, list):
            return [replace_placeholders(i, replacements) for i in data]
        elif isinstance(data, str):
            for placeholder, value in replacements.items():
                data = data.replace(f"{{{placeholder}}}", value)
            return data
        else:
            return data

    try:
        # Check if file_input is a string (file path) or a file object
        if isinstance(file_input, str):
            with open(file_input, 'r') as file:
                yaml_data = yaml.safe_load(file)
        else:
            yaml_data = yaml.safe_load(file_input)
    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"Error parsing YAML file: {e}")

    # Find all local variables (strings) at the top level
    local_vars = find_local_vars(yaml_data)

    # Replace placeholders
    processed_data = replace_placeholders(yaml_data, local_vars)

    return processed_data