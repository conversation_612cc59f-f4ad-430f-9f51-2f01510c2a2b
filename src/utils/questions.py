import json

class QuestionsUtils:
    def __init__(self, json_path):
        self.json_path = json_path
        self.data = self.load_json()

    def load_json(self):
        with open(self.json_path, 'r') as file:
            data = json.load(file)
        return data

    def get_basic_questions(self):
        return [item['question'] for item in self.data.get("basic_questions", [])]

    def get_basic_context(self):
        return [item['context'] for item in self.data.get("basic_questions", [])]

    def get_basic_questions_with_context(self):
        return [f"{item['question']} ({item['context']})" for item in self.data.get("basic_questions", [])]

    def get_deconstructor_questions(self):
        return [item['question'] for item in self.data.get("deconstructor", [])]

    def get_deconstructor_context(self):
        return [item['context'] for item in self.data.get("deconstructor", [])]

    def get_deconstructor_questions_with_context(self):
        return [f"{item['question']} ({item['context']})" for item in self.data.get("deconstructor", [])]

    def get_constructor_actions(self):
        return self.data.get("constructor", [])

# Example usage
if __name__ == "__main__":
    utils = QuestionsUtils("config/prompt/actions.json")

    print("Basic Questions:")
    print(utils.get_basic_questions())

    print("\nBasic Contexts:")
    print(utils.get_basic_context())

    print("\nBasic Questions with Contexts:")
    print(utils.get_basic_questions_with_context())

    print("\nConstructor Actions:")
    print(utils.get_constructor_actions())

    print("\nDeconstructor Questions:")
    print(utils.get_deconstructor_questions())

    print("\nDeconstructor Contexts:")
    print(utils.get_deconstructor_context())

    print("\nDeconstructor Questions with Contexts:")
    print(utils.get_deconstructor_questions_with_context())
