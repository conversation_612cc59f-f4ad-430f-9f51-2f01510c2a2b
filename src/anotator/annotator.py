import pandas as pd
from PIL import Image
from tqdm import tqdm
from src.utils.annotation import (
    get_frame_at_time,
    standardize_frame,
    pil_to_base64,
    call_gpt4_vision,
    token_count_multi,
    parse_time_str
)

def annotate_clip_sliding(video_path, clip_time, ground_class, video_name):
    start_str, end_str = clip_time.split("-")
    start_sec, end_sec = parse_time_str(start_str), parse_time_str(end_str)
    results = []
    for t in range(start_sec + 1, end_sec):
        frame_prev = get_frame_at_time(video_path, t - 1)
        frame_mid  = get_frame_at_time(video_path, t)
        frame_next = get_frame_at_time(video_path, t + 1)
        if None in (frame_prev, frame_mid, frame_next):
            continue
        frames = [frame_prev, frame_mid, frame_next]
        std_frames = standardize_frame(frames, target_size=512)
        images_b64 = []
        for frame in std_frames:
            if not hasattr(frame, "convert"):
                frame = Image.fromarray(frame)
            images_b64.append(pil_to_base64(frame))
        prompt = (
            f"Analyze the middle frame of this 3-frame window from video '{video_name}', "
            f"which is classified as '{ground_class}'. Using the previous and next frames as context, "
            "provide a three-word summary: action, object, attribute."
        )
        annotation = call_gpt4_vision(prompt, images_b64)
        tokens = token_count_multi(prompt, annotation)
        results.append({
            "video_name": video_name,
            "clip_time": clip_time,
            "frame_time": t,
            "annotation": annotation,
            "input_tokens": tokens["input_tokens"],
            "output_tokens": tokens["output_tokens"]
        })
    return results

def process_csv_sliding(csv_path, output_csv, num_clips=25):
    df = pd.read_csv(csv_path).head(num_clips)
    all_results = []
    for _, row in tqdm(df.iterrows(), total=len(df), desc="Processing clips"):
        clip_results = annotate_clip_sliding(row["video_path"], row["Clip time"], row["Class"], row["Video name"])
        if clip_results:
            all_results.extend(clip_results)
    out_df = pd.DataFrame(all_results)
    out_df.to_csv(output_csv, index=False)
    return out_df

if __name__ == "__main__":
    csv_path = "/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/assets/new_usecase_with_paths.csv"
    output_csv = "/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/data/assets/annotations.csv"
    process_csv_sliding(csv_path, output_csv, num_clips=25)
