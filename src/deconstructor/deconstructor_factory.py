from src.utils.yaml_utils import load_yaml
from src.deconstructor.deconstructor import Deconstructor
from src.deconstructor.deconstructor_lean import DeconstructorLean

class DeconstructorFactory:
    @staticmethod
    def create_deconstructor(video_path, config_path, image, actions_path, prompts_path, context_path):
        config = load_yaml(config_path)
        if config.get('pipeline', {}).get('use_lean_deconstructor', False):
            return DeconstructorLean(video_path, config_path, image)
        return Deconstructor(config_path, image, actions_path, prompts_path, context_path) 