import json
import os
from abc import ABC
from transformers import <PERSON>Tokenizer, AutoModel
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from collections import deque
import torch
import spacy
from src.deconstructor.base_deconstructor import BaseDeconstructor
from src.utils.yaml_utils import load_yaml
from src.vlm.vlm_factory import VLMFactory
from src.utils.metrics import inference_metrics
#from scripts.token_mapper import process_tokens
import time


class DeconstructorLean(BaseDeconstructor):
    def __init__(self, video_path, config_path, image, json_path="config/prompt/actions_pattern.json"): 
        print("\n=== Initializing DeconstructorLean ===")

        # Load configuration
        self.video_path = video_path
        self.config = load_yaml(config_path)
        self.image = image
        self.matching_strategy = self.config['matching']['strategy']  # "keyword", "embedding", or "both"
        self.nlp = spacy.load("en_core_web_sm")   #preprocessing text


        # Initialize VLM interaction
        self.vlm_interaction = VLMFactory.create_vlm(self.config)
        if not self.vlm_interaction:
            raise ValueError("Failed to initialize VLM interaction. Check configuration.")
        

        # Load action patterns from JSON
        self.action_patterns = self.load_action_patterns(json_path)


        if self.matching_strategy in ["embedding", "hybrid"]:
            #Ensure embeddings are computed only once
            self.embedding_model_name = self.config['embedding']['model']
            #Load model only once
            self.load_model()  
            #Precompute embeddings only once
            self.precompute_use_case_embeddings()  


        #Initialize TF-IDF vectorizer and matrix
        self.tfidf_vectorizer = None
        self.tfidf_matrix = None
        if self.matching_strategy in ["tfidf"]:
            self.precompute_tfidf_matrix()
        

        #Add prompt templates
        self.base_prompt = """You are a security surveillance system analyzing video frames (up to 10 seconds in duration).
    Focus specifically on detecting either:
    1. Vehicle obstructions - vehicles blocking roads, entrances, or critical pathways
    2. Robbery incidents - forceful theft, threatening behavior, or armed individuals

    Your task is to describe any suspicious actions related ONLY to vehicle obstruction or robbery.
    Do not fabricate details beyond what is clearly visible in the frames.

    IMPORTANT GUIDELINES:
    - Respond with EXACTLY FOUR words
    - First word: ACTION verb (e.g., 'blocking', 'threatening', 'robbing')
    - Second word: PRIMARY OBJECT (e.g., 'truck', 'car', 'robber')
    - Third word: QUALIFIER (e.g., 'forcefully', 'aggressively', 'completely')
    - Fourth word: LOCATION (e.g., 'entrance', 'driveway', 'store')
    
    Examples: 
    - 'blocking truck illegally driveway'
    - 'threatening robber aggressively cashier'
    - 'obstructing vehicle completely entrance'
    - 'robbing person forcefully store'

    If no vehicle obstruction or robbery is detected, respond with: 'No suspicious activity detected'

    Additional focus points:
    {}

    Describe the main action:"""


        self.triplet_prompt = """You are a security surveillance system analyzing video frames (max 10 seconds).
    Focus ONLY on detecting:
    - Vehicle obstruction incidents
    - Robbery-related activities

    Describe the main suspicious action in EXACTLY three words (action object location).

    IMPORTANT:
    - Use EXACTLY three words
    - First word - ACTION verb (blocking/robbing)
    - Second word - OBJECT (vehicle/person)
    - Third word - LOCATION (entrance/store)
    - Examples: 'blocking truck entrance', 'robbing store front'
    - If no relevant activity, respond: 'No Action <location>'

    Additional focus points:
    {}

    Describe the main action:"""



        # Additional configurations
        self.INSTRUCTION_QUEUE_SIZE = 2
        self.current_instructions = deque(maxlen=self.INSTRUCTION_QUEUE_SIZE)
        self.frame_counter = 0
        self.TRIPLET_INTERVAL = 5

        print("=== Initialization Complete ===\n")


    def load_action_patterns(self, json_path):
        """
        Load action patterns from an external JSON file.
        """
        try:
            with open(json_path, "r") as f:
                action_patterns = json.load(f)
            print("Action patterns loaded successfully from JSON.")
            return action_patterns
        except Exception as e:
            print(f"Error loading action patterns: {e}")
            return {}


    def load_model(self):
        """
        Load the required embedding model only if it hasn't been loaded yet.
        """
        if not hasattr(self, "model"):  
            if not self.embedding_model_name:
                raise ValueError("Embedding model name is not defined in the configuration.")

            print(f"Loading embedding model: {self.embedding_model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.embedding_model_name)
            self.model = AutoModel.from_pretrained(self.embedding_model_name)
            print("Embedding model loaded successfully.")



    def preprocess_vlm_output(self, vlm_output):
        """
        Preprocess the VLM output by cleaning the text, removing stopwords,
        and reducing words to their base (lemma) forms using spaCy.
        """
        # Remove unwanted characters, convert to lowercase, and strip whitespace.
        vlm_output = ''.join(char for char in vlm_output if char.isalnum() or char.isspace()).lower().strip()
        
        # Check for special cases where processing is not needed.
        if vlm_output in ["no action", "no match", ""]:
            return None
        
        # Process the text with spaCy
        doc = self.nlp(vlm_output)
        
        # Remove stopwords and lemmatize each token
        lemmatized_tokens = [token.lemma_ for token in doc if not token.is_stop]
        
        # Join the tokens back into a string and return the result.
        return ' '.join(lemmatized_tokens)


    ###KEYWORD MATCHING###
    def keyword_matching(self, preprocessed_text):
        """
        Perform keyword-based matching by mapping the preprocessed output to the dictionary.
        This function expects the text to have been preprocessed with spaCy (i.e., lowercased,
        lemmatized, and stopwords removed), so it simply splits the text into tokens for matching.
        """
        # If the preprocessed text is None or empty, return immediately.
        if not preprocessed_text:
            print("No preprocessed text provided for matching.")
            return "No Match", "None"
        
        # Split the already preprocessed text into tokens.
        tokens = preprocessed_text.split()
        print(f"Tokens: {tokens}")

        # Iterate over each pattern and check for a match.
        for pattern_name, pattern in self.action_patterns.items():
            primary_match = any(token in pattern['primary_keywords'] for token in tokens)
            secondary_match = any(token in pattern['secondary_keywords'] for token in tokens)

            # Return a match if at least one primary and one secondary keyword exist.
            if primary_match and secondary_match:
                print(f"Matched Pattern: {pattern_name}")
                return pattern_name, pattern['instruction']

        print("No match found in keyword matching.")
        return "No Match", "None"



    ### EMBEDDINGS MATCHING###
    def embed_texts(self, texts):
        start_time = time.time()
        inputs = self.tokenizer(texts, padding=True, truncation=True, return_tensors="pt")
        with torch.no_grad():
            outputs = self.model(**inputs)
        embeddings = outputs.last_hidden_state.mean(dim=1).cpu().numpy()
        end_time = time.time()
        print("Embedding time: {} seconds".format(end_time - start_time))
        return embeddings
    

    def precompute_use_case_embeddings(self):
        """
        Precompute embeddings for the 'use_case' descriptions in self.action_patterns.
        """
        print("\n=== Precomputing Use Case Embeddings ===")
        for pattern_name, pattern in self.action_patterns.items():
            use_case_text = pattern['use_case']
            pattern['use_case_embedding'] = self.embed_texts([use_case_text])[0]
        print("Precomputed embeddings for use_case descriptions in action patterns.")


    def embedding_matching(self, vlm_output):
        """
        Perform embedding-based matching using the precomputed 'use_case' embeddings in self.action_patterns.
        """
        if not vlm_output:
            return "No Match", "None" 

        vlm_embedding = self.embed_texts([vlm_output])[0]  # Generate embedding for the VLM output
        similarity_threshold = self.config['embedding'].get('similarity_threshold', 0.50)

        best_match = None
        best_score = -1

        # Compare the VLM embedding with use_case embeddings
        for pattern_name, pattern in self.action_patterns.items():
            use_case_embedding = pattern.get('use_case_embedding')
            if use_case_embedding is not None:
                action_similarity = cosine_similarity([vlm_embedding], [use_case_embedding]).flatten()[0]
                if action_similarity > best_score and action_similarity >= similarity_threshold:
                    best_score = action_similarity
                    best_match = pattern_name

        if best_match:
            print(f"Matched Action Pattern: {best_match} | Similarity Score: {best_score:.2f}")
            instruction = self.action_patterns[best_match]['instruction']
            return best_match, instruction

        print("No action pattern met the similarity threshold.")
        return "No Match", "None"



    ###TF-IDF MATCHING###
    def precompute_tfidf_matrix(self):
        """
        Precompute the TF-IDF matrix for the 'use_case' descriptions in self.action_patterns.
        """
        print("\n=== Precomputing TF-IDF Matrix using Use Cases ===")
        
        # Extract use_case descriptions from action_patterns
        use_case_descriptions = [pattern['use_case'] for pattern in self.action_patterns.values()]
        
        # Initialize the TF-IDF vectorizer and compute the matrix
        self.tfidf_vectorizer = TfidfVectorizer()
        self.tfidf_matrix = self.tfidf_vectorizer.fit_transform(use_case_descriptions)

        print("TF-IDF matrix computed successfully from use cases.")


    def tfidf_matching(self, vlm_output):
        """
        Perform TF-IDF-based matching using cosine similarity between the VLM output 
        and the TF-IDF matrix built from action pattern use cases.
        """
        if not vlm_output:
            return "No Match", "None"

        if self.tfidf_matrix is None or self.tfidf_matrix.shape[0] == 0:
            return "No Match", "None"

        # Convert the VLM output into a TF-IDF vector
        vlm_tfidf_vector = self.tfidf_vectorizer.transform([vlm_output])

        # Compute cosine similarity between the VLM TF-IDF and the precomputed matrix
        similarity_scores = cosine_similarity(vlm_tfidf_vector, self.tfidf_matrix).flatten()

        if len(similarity_scores) == 0:
            print("No similarity scores found.")
            return "No Match", "None"

        # Find the best match by highest similarity score
        best_index = similarity_scores.argmax()
        best_score = similarity_scores[best_index]

        # Compare to threshold
        similarity_threshold = self.config['tfidf'].get('similarity_threshold', 0.75)
        if best_score >= similarity_threshold:
            matched_use_case = list(self.action_patterns.keys())[best_index]  # Get matching key
            print(f"Matched TF-IDF Use Case: {matched_use_case} | Similarity Score: {best_score:.2f}")
            return matched_use_case, self.action_patterns[matched_use_case]['instruction']

        print("No TF-IDF match found.")
        return "No Match", "None"



    def get_current_prompt(self):
        """
        Generate the current VLM (Vision-Language Model) prompt.
        
        This method constructs a prompt based on current instructions and switches between
        two types of prompts (triplet and base) at fixed intervals.

        Returns:
            str: The formatted prompt based on the current frame count.
        """

        # 1. Combine current instructions into a single string, separated by new lines.
        #    If no instructions are provided, use a default instruction.
        instructions = "\n".join(self.current_instructions) if self.current_instructions else "Watch for any suspicious behavior."

        # 2. Increment the frame counter each time this method is called.
        self.frame_counter += 1

        # 3. Check if the frame counter has reached a multiple of TRIPLET_INTERVAL.
        #    If yes, return the 'triplet_prompt' formatted with the current instructions.
        if self.frame_counter % self.TRIPLET_INTERVAL == 0:
            return self.triplet_prompt.format(instructions)

        # 4. Otherwise, return the 'base_prompt' formatted with the current instructions.
        else:
            return self.base_prompt.format(instructions)
        


    #function used to calcuate the context(called from pipeline_script)
    @inference_metrics # Decorator to measure inference metrics
    def compute_context(self, images=None):
        """
        Compute context by sending a prompt (and one or more images) to the VLM.
        If images is not provided, uses self.image.
        Returns a tuple: (matched_class, instruction, match_found)
        where match_found is True if any matching instruction (via keyword/embedding/tfidf)
        is detected.
        """
        current_prompt = self.get_current_prompt()
        # Use images if provided; otherwise, fall back to the single image in self.image.
        if images is None:
            images = [self.image]
            
        start_time = time.time()
        if self.config['vlm']['provider'] == "huggingface":
            response = self.vlm_interaction.predict(([self.image], [current_prompt]))
        elif self.config['vlm']['provider'] == "vllm":
            # Here we assume that self.vlm_interaction.create_message exists (or use our own)
            message = self.vlm_interaction.create_message(images, current_prompt)
            response = self.vlm_interaction.predict(message)
        else:
            response = self.vlm_interaction.predict((self.image, [current_prompt]))
        end_time = time.time()
        print(f"VLM inference time: {end_time - start_time:.4f} seconds")
        
        # For Hugging Face, we assume response is a list; for vllm, response is a string.
        if self.config['vlm']['provider'] == "vllm":
            vlm_output = response
        else:
            vlm_output = response[0]
        
        print(f"VLM Output (Raw): {vlm_output}")
        processed_output = self.preprocess_vlm_output(vlm_output)
        print(f"Processed text: {processed_output}")
        
        if not processed_output:
            return "No Match", "None", False , vlm_output
        
        # Initialize
        matched_class, instruction = "No Match", None
        match_found = False
        
        # Keyword matching
        result_class, result_instruction = self.keyword_matching(processed_output)
        if result_class != "No Match":
            print(f"Keyword Match Found: {result_class} | Instruction: {result_instruction}")
            matched_class, instruction = result_class, result_instruction
            match_found = True

        # Embedding matching if necessary
        if self.matching_strategy in ["embedding", "hybrid"] and matched_class == "No Match":
            result_class, result_instruction = self.embedding_matching(processed_output)
            if result_class != "No Match":
                print(f"Embedding Match Found: {result_class} | Instruction: {result_instruction}")
                matched_class, instruction = result_class, result_instruction
                match_found = True

        # TF-IDF matching
        if self.matching_strategy in ["tfidf"] and matched_class == "No Match":
            result_class, result_instruction = self.tfidf_matching(processed_output)
            if result_class != "No Match":
                print(f"TF-IDF Match Found: {result_class} | Instruction: {result_instruction}")
                matched_class, instruction = result_class, result_instruction
                match_found = True

        # Check for duplicate instruction before adding
        if instruction and instruction not in self.current_instructions:
            self.current_instructions.append(instruction)
            print(f"Instruction Added to Queue: {instruction}")
        else:
            print(f"Duplicate Instruction Ignored: {instruction}")

        return matched_class, instruction, match_found,vlm_output
