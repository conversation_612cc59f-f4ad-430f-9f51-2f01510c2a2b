import numpy as np
from openai import OpenAI
from src.vector_stores.vector_store_factory import VectorStoreFactory
from src.utils.helpers import load_json
from src.utils.yaml_utils import load_yaml
from src.vlm.vlm_factory import VLMFactory
from src.base.base_component import BaseComponent
from src.utils.questions import QuestionsUtils

class Deconstructor(BaseComponent):
    def __init__(self, config_path, image_path, actions_path, prompts_path, context_path):
        self.config = load_yaml(config_path)
        self.image_path = image_path
        self.prompts = load_json(prompts_path)
        self.context = load_json(context_path)
        self.vlm_interaction = VLMFactory.create_vlm(self.config)
        self.client = OpenAI(api_key=self.config['openai_api_key'])
        self.vector_store = VectorStoreFactory.create_vector_store(self.config)
        self.questions_utils = QuestionsUtils(actions_path)
        self.num_questions = self.config['pipeline']['deconstructor']['num_questions']
        self.basic_responses = None
        self.scene_info = None

    def get_embedding(self, text, model="text-embedding-3-small"):
        text = text.replace("\n", " ")
        response = self.client.embeddings.create(input=[text], model=model) 
        embedding = response.data[0].embedding
        return embedding
    
    def find_top_questions(self, responses):
        combined_response = " ".join(responses)
        print("data used to find matches: ", combined_response)
        query_embedding = self.get_embedding(combined_response, model=self.config['embedding']['model'])
        top_indices, distances = self.vector_store.search(query_embedding, top_k=self.num_questions)
        top_questions = []
        seen_indices = set()
        counter = 1
        deconstructor_questions = self.questions_utils.get_deconstructor_questions()
        for i in top_indices:
            if i not in seen_indices and i < len(deconstructor_questions):
                top_questions.append(str(counter) + ". " + deconstructor_questions[i])
                seen_indices.add(i)
                counter += 1
            if len(top_questions) >= self.num_questions:
                break
        return top_questions

    def ask_basic_questions(self):
        basic_questions = self.questions_utils.get_basic_questions()
        numbered_questions = [f"{i+1}. {q}" for i, q in enumerate(basic_questions)]
        questions = [self.prompts["basic_questions_header"]] + numbered_questions
        return self.vlm_interaction.predict((self.image_path, questions))

    def ask_top_questions(self, top_questions):
        questions_with_prompt = f"{self.prompts['base_prompt']} \n\n" + "\n\n".join([f"{i+1}. {q}" for i, q in enumerate(top_questions)])
        return self.vlm_interaction.predict((self.image_path, [questions_with_prompt]))

    def compute_context(self):
        # Step 1: Ask basic questions
        self.basic_responses = self.ask_basic_questions()
        print("Basic Responses:", self.basic_responses)

        # Combine basic responses into scene information
        basic_questions = self.questions_utils.get_basic_questions()
        self.scene_info = "\n".join(
            [f"{i+1}. {q} Answer: {self.basic_responses[i] if i < len(self.basic_responses) else 'N/A'}"
             for i, q in enumerate(basic_questions)]
        )

    def compute_top_questions(self):
        # Step 2: Find top 5 relevant questions based on basic responses
        if not self.basic_responses:
            self.compute_context()
        top_questions = self.find_top_questions(self.basic_responses)
        print("Top Questions:", top_questions)

        # Step 3: Ask top 5 relevant questions
        top_responses = self.ask_top_questions(top_questions)
        print("Top Question Responses:", top_responses)

        return self.scene_info, "\n".join(
            [f"{i+1}. {q} Answer: {top_responses[i] if i < len(top_responses) else 'N/A'}"
             for i, q in enumerate(top_questions)]
        )

    def execute(self):
        self.compute_context()
        return self.compute_top_questions()
