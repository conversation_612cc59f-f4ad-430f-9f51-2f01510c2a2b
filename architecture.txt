+--------------------------------------------------------------------------------------------------------------------------------+
|                                    Constuctor - Deconstructor Architecture                                                     |
+--------------------------------------------------------------------------------------------------------------------------------+
|                                                                                                                                |
+--------------------------------------------------------------------------------------------------------------------------------+
|                                               Pipelines                                                                        |
|--------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                |
|  +-----------------------------+                    +-----------------------------+                                            |
|  |       Image Pipeline        |                    |       Video Pipeline        |                                            |
|  |  - Load/Encode/process  ✅  |                    |  - Load/Sample/process ✅  |                                            |
|  +-----------------------------+                    +-----------------------------+                                            |
|                                          ↓                                                                                     |
|                                          ↓                                                                                     |
|                       +----------------------------------------------------------+                                             |                                                                                            |  |
|                       |  - Call VLM (Deconstructor)          ✅                  |                                             |
|                       |  - Call LLM (Constructor)            ✅                  |                                             |
|                       |  - Get Actionable Response           ✅                  |                                             |
|                       +----------------------------------------------------------+                                             |
|                                                                                                                                |
+--------------------------------------------------------------------------------------------------------------------------------+
|                                                                                                                                |
|                                                  Modules                                                                       |
|                                                                                                                                |
|  +-------------------------------------------------------------------+      +------------------------------------------------+ |
|  |                          Deconstructor                            |      |                 Constructor                    | |
|  |-------------------------------------------------------------------|      |------------------------------------------------| |
|  |  - Base + deconstructor factory                                 ✅|      | - Base + constructor factory                ✅| |
|  |  - Initialize with config, actions, prompts, context            ✅|      | - Initialize with config & prompts (openai) ✅| |
|  |  - Load image/video frames                                      ✅|      | - Prepare inputs for constructor            ✅| |
|  |  - Interact with Visual Large Model (VLM)                       ✅|      | - executable outputs                        ✅| |
|  |  - Ask basic questions                                          ✅|      | - Unittesting + Integration w/ pipeline     ✅| |
|  |  - Compute context                                              ✅|      | - Other LLMs than gpt                       ❌| |
|  |  - Find top questions using vector search                       ✅|      |                                               |  |
|  |  - Ask top questions  & Combine responses                       ✅|      |                                               |  |
|  |  - Unittesting + Integration w/ pipeline                        ✅|      |                                               |  |
|  |                                                                   |      |                                                | |
|  |                                                                   |      |                                                | |
|  +-------------------------------------------------------------------+      +------------------------------------------------+ |
|                                                                                                                                |
|                                                                                                                                |
|  +-------------------------------------------------------------------+                                                         |
|  |                          Vector Store                             |                                                         |
|  |-------------------------------------------------------------------|                                                         |
|  |  - Create vector store (Faiss)                                 ✅|                                                          |
|  |  - Vectorize and store embeddings                              ✅|                                                          |
|  |  - Search for relevant embeddings                              ✅|                                                          |
|  |  - Use Milvus/Qdrant for vector store                          ❌|                                                          |
|  |  - Unittest + integrate vector search with pipeline            ✅|                                                         |
|  |                                                                   |                                                         |
|  +-------------------------------------------------------------------+                                                         |
|                                                                                                                                |
|                                                                                                                                |
|  +---------------------------------------------------+    +-----------------------------+    +-----------------------+          |
|  |                       LLM                         |    |              VLM            |    |      Question/Context |          |
|  |---------------------------------------------------|    |-----------------------------|    |-----------------------|          |
|  |  - Base module + LLM Factory                    ✅|   | - Base + VLM FActory      ✅|    | - Parse questions  ✅|          |
|  |  - Initialize w/ config, interact               ✅|   | - ollama & Hf integration ✅|    | - Parse context    ✅|          |
|  |  - unittest + integrate w/ main/pipelines       ✅|   | - integrate w/ pipeline   ✅|    | - custom loaders   ✅|          |
|  |                                                   |    | - Video VLM               🟡|    |                       |         |
|  |                                                   |    | - Multi-image VLM         🟡|    |                       |         |
|  +---------------------------------------------------+    +-----------------------------+    +-----------------------+         |
|                                                                                                                                |
|  +--------------------------------------------------+    +----------------------+    +---------------------------------+       |
|  |  Config + loader                                 |    |  Actions             |    |  Prompts                        |       |
|  |--------------------------------------------------|    |----------------------|    |---------------------------------|       |
|  |  - Load configuration                         ✅|    | - define actions   ✅|    | - Construct(template) prompts ✅|       |
|  |  - Model settings + params                    ✅|    | - Deconstructor    ✅|    | - Basic questions header      ✅|       |
|  |  - API keys, video params etc                 ✅|    | - Constructor      ✅|    | - Constructor prompt          ✅|       |
|  |                                                  |    |                      |    |                                 |       |
|  |                                                  |    |                      |    |                                 |       |
|  +--------------------------------------------------+    +----------------------+    +---------------------------------+       |
|                                                                                                                                |
|                                                                                                                                |
|                                                                                                                                |
+---------------------------------------------------------------------------------------------------------------------------------+
                                   ✅ - Completed    🟡 - In Progress   ❌ - To be Done