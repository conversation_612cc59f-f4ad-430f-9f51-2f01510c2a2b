Project Structure:
lluminaai/
    __init__.py
    mp4_to_stream.py
    setup.py
    summarize.py
    scripts/
        token_mapper.py
        main.py
        vectorize_data.py
        main_stream.py
    config/
        prompt/
        model/
    data/
        eval/
            hugging_face/
                security_breach_1.f134/
                robbery_5/
        processed/
            office/
                qween_2b/
                janus_7b/
                janus_1b/
                llava_13b/
        assets/
        vector_store/
            apps/
                office/
    src/
        __init__.py
        vector_stores/
            __init__.py
            milvus_store.py
            faiss_store.py
            qdrant_store.py
            vector_store_factory.py
            base_vector_store.py
        nx/
            nx_api.py
        deconstructor/
            __init__.py
            base_deconstructor.py
            deconstructor.py
            deconstructor_factory.py
            deconstructor_lean.py
        base/
            __init__.py
            base_component.py
            base_model.py
        utils/
            yaml_utils.py
            annotation.py
            __init__.py
            stream.py
            helpers.py
            metrics.py
            video_utils.py
            questions.py
            logging.py
        rag/
            langchain.py
            __init__.py
            rag.py
        constructor/
            constructor.py
            __init__.py
            base_constructor.py
        pipelines/
            __init__.py
            pipeline_video_basic.py
            pipeline_video_stream_lean.py
            pipeline_image_basic.py
            pipeline_video_stream.py
            pipeline_video_basic_lean.py
        anotator/
            annotator.py
        agents/
            __init__.py
            agent_manager.py
        llms/
            base_lm.py
            openai_lm.py
            lm_factory.py
        vlm/
            vllm_vlm.py
            ollama_vlm.py
            huggingface_vlm.py
            vlm_interaction.py
            janus.py
            vlm_factory.py
            janus/
                __init__.py
                models/
                    __init__.py
                    projector.py
                    clip_encoder.py
                    vq_model.py
                    modeling_vlm.py
                    siglip_vit.py
                    image_processing_vlm.py
                    processing_vlm.py
                utils/
                    conversation.py
                    __init__.py
                    io.py
                janusflow/
                    __init__.py
                    models/
                        __init__.py
                        clip_encoder.py
                        modeling_vlm.py
                        uvit.py
                        siglip_vit.py
                        image_processing_vlm.py
                        processing_vlm.py
    Janus/
    demo/
        streamlit_basic.py
        streamlit_token_viz.py
        streamlit_live_basic.py

Code Overviews:

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/mp4_to_stream.py:
Import: sys
Import: gi
From gi.repository import Gst
From gi.repository import GstRtspServer
From gi.repository import GObject
From gi.repository import GLib
Class: TestRtspMediaFactory
Class: GstreamerRtspServer
Function: __init__
Function: do_create_element
Function: __init__

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/summarize.py:
Import: os
Import: ast
Function: should_ignore
Function: get_directory_tree
Function: get_code_overview
Function: generate_project_overview

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/scripts/token_mapper.py:
Import: json
Import: os
Import: spacy
From openai import OpenAI
Import: yaml
Function: load_config
Function: ask_gpt
Function: load_json
  Description: Load JSON data from a file.
Function: save_json
  Description: Save updated keyword dictionary to JSON file.
Function: find_best_class
  Description: Finds the correct related class from usecase_actions_mapped based on the given use case.
Function: classify_tokens
  Description: Classifies tokens into verbs and other words using NLP.

    Parameters:
        tokens (list): List of words (not characters).

    Returns:
        tuple: (set of verbs, set of other words)
Function: match_tokens_to_class
  Description: Matches VLM output tokens to a related class and checks if they match its keywords.
    If new keywords are found, they are added to the JSON file.

    Parameters:
        use_case (str): The use case name (e.g., 'burglary').
        tokens (set): Set of tokens from the VLM model.
        keyword_dict (dict): The dictionary containing keyword mappings.
        usecase_actions_mapped (dict): The dictionary mapping use cases to their related classes.
        json_path (str): Path to the keyword dictionary JSON file.

    Returns:
        dict: Mapped results with class, keyword matches, and status.
Function: process_tokens
  Description: Process tokens and directory name against a keyword JSON dictionary.
    
    Parameters:
        json_path (str): Path to the keyword dictionary JSON file.
        video_dir (str): Full video directory path.
        tokens (list): List of tokens generated by VLM.
    
    Returns:
        dict: Mapped results with class, keyword matches, and status.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/scripts/main.py:
Import: sys
Import: re
Import: os
Import: json
Import: argparse
Import: numpy
From src.pipelines import pipeline_image_basic
From src.pipelines import pipeline_video_basic
From src.pipelines import pipeline_video_stream_lean
From src.pipelines import pipeline_video_basic_lean
From src.utils.yaml_utils import load_yaml
Function: process_video
  Description: Process a video using the configured pipeline.
Function: main

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/scripts/vectorize_data.py:
Import: yaml
Import: json
From openai import OpenAI
Import: numpy
From src.vector_stores.vector_store_factory import VectorStoreFactory
From src.utils.helpers import load_json
From src.utils.questions import QuestionsUtils
Import: os
Import: argparse
From src.utils.yaml_utils import load_yaml
Function: load_config
Function: get_embedding
Function: vectorize_data

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/scripts/main_stream.py:
Import: sys
Import: re
Import: os
Import: json
Import: argparse
Import: yaml
From src.pipelines import pipeline_image_basic
From src.pipelines import pipeline_video_stream
From src.utils.stream import create_rtsp_url
From src.utils.stream import load_stream_config
From pdb import set_trace
Function: append_to_alerts_file
Function: clean_json_response
Function: load_config
Function: main

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vector_stores/faiss_store.py:
Import: faiss
Import: numpy
Import: os
Import: hashlib
From base_vector_store import BaseVectorStore
From scripts.vectorize_data import vectorize_data
Class: FaissStore
Function: __init__
Function: _load_existing_hashes
Function: _save_hashes
Function: _hash_vector
Function: add_vectors
Function: save
Function: search
Function: embedding_exists

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vector_stores/vector_store_factory.py:
Import: importlib
Class: VectorStoreFactory
Function: create_vector_store

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vector_stores/base_vector_store.py:
From abc import ABC
From abc import abstractmethod
Class: BaseVectorStore
Function: add_vectors
Function: save
Function: search

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/nx/nx_api.py:
Import: requests
Import: json
Import: time
Class: NxAPI
Function: __init__
Function: login
  Description: Logs in to the NX Witness server.
        This sends a JSON payload to /rest/v3/login/sessions and on success extracts the token.
        It then fetches devices and stores any with 'GENERIC_RTSP-stream' in their name.
Function: send_event
  Description: Sends an event to the NX Witness server.
        The event JSON includes a type, caption, description, state, and metadata.
Function: delete_current_session
  Description: Deletes the current session (logs out) from the NX Witness server.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/deconstructor/base_deconstructor.py:
From abc import ABC
From abc import abstractmethod
Class: BaseDeconstructor
Function: load_model

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/deconstructor/deconstructor.py:
Import: numpy
From openai import OpenAI
From src.vector_stores.vector_store_factory import VectorStoreFactory
From src.utils.helpers import load_json
From src.utils.yaml_utils import load_yaml
From src.vlm.vlm_factory import VLMFactory
From src.base.base_component import BaseComponent
From src.utils.questions import QuestionsUtils
Class: Deconstructor
Function: __init__
Function: get_embedding
Function: find_top_questions
Function: ask_basic_questions
Function: ask_top_questions
Function: compute_context
Function: compute_top_questions
Function: execute

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/deconstructor/deconstructor_factory.py:
From src.utils.yaml_utils import load_yaml
From src.deconstructor.deconstructor import Deconstructor
From src.deconstructor.deconstructor_lean import DeconstructorLean
Class: DeconstructorFactory
Function: create_deconstructor

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/deconstructor/deconstructor_lean.py:
Import: json
Import: os
From abc import ABC
From transformers import AutoTokenizer
From transformers import AutoModel
From sklearn.feature_extraction.text import TfidfVectorizer
From sklearn.metrics.pairwise import cosine_similarity
From collections import deque
Import: torch
Import: spacy
From src.deconstructor.base_deconstructor import BaseDeconstructor
From src.utils.yaml_utils import load_yaml
From src.vlm.vlm_factory import VLMFactory
From src.utils.metrics import inference_metrics
Import: time
Class: DeconstructorLean
Function: __init__
Function: load_action_patterns
  Description: Load action patterns from an external JSON file.
Function: load_model
  Description: Load the required embedding model only if it hasn't been loaded yet.
Function: preprocess_vlm_output
  Description: Preprocess the VLM output by cleaning the text, removing stopwords,
        and reducing words to their base (lemma) forms using spaCy.
Function: keyword_matching
  Description: Perform keyword-based matching by mapping the preprocessed output to the dictionary.
        This function expects the text to have been preprocessed with spaCy (i.e., lowercased,
        lemmatized, and stopwords removed), so it simply splits the text into tokens for matching.
Function: embed_texts
Function: precompute_use_case_embeddings
  Description: Precompute embeddings for the 'use_case' descriptions in self.action_patterns.
Function: embedding_matching
  Description: Perform embedding-based matching using the precomputed 'use_case' embeddings in self.action_patterns.
Function: precompute_tfidf_matrix
  Description: Precompute the TF-IDF matrix for the 'use_case' descriptions in self.action_patterns.
Function: tfidf_matching
  Description: Perform TF-IDF-based matching using cosine similarity between the VLM output 
        and the TF-IDF matrix built from action pattern use cases.
Function: get_current_prompt
  Description: Generate the current VLM (Vision-Language Model) prompt.
        
        This method constructs a prompt based on current instructions and switches between
        two types of prompts (triplet and base) at fixed intervals.

        Returns:
            str: The formatted prompt based on the current frame count.
Function: compute_context
  Description: Compute context by sending a prompt (and one or more images) to the VLM.
        If images is not provided, uses self.image.
        Returns a tuple: (matched_class, instruction, match_found)
        where match_found is True if any matching instruction (via keyword/embedding/tfidf)
        is detected.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/base/base_component.py:
From abc import ABC
From abc import abstractmethod
Class: BaseComponent
Function: execute

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/base/base_model.py:
From abc import ABC
From abc import abstractmethod
Class: BaseModel
Function: load_model
Function: predict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/yaml_utils.py:
Import: yaml
From typing import Dict
From typing import Any
From typing import Union
From io import TextIOWrapper
Function: load_yaml
  Description: Load a YAML file or file object, replace placeholders with local variable values, and return the processed data.

    Placeholders in the YAML file should be in the format {variable_name}.
    The function will look for any variables defined at the top level of the YAML
    and use their values to replace corresponding placeholders in other strings.

    Args:
    file_input (Union[str, TextIOWrapper]): Path to the YAML file or an opened file object.

    Returns:
    Dict[str, Any]: Processed YAML data with placeholders replaced.

    Raises:
    yaml.YAMLError: If there's an error parsing the YAML file.
Function: find_local_vars
Function: replace_placeholders

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/annotation.py:
Import: cv2
Import: os
Import: numpy
Import: base64
Import: tiktoken
From openai import OpenAI
From PIL import Image
From io import BytesIO
Function: load_video_frames
Function: standardize_frame
Function: save_frame
Function: get_frame_at_time
Function: pil_to_base64
Function: call_gpt4_vision
Function: token_count_multi
Function: parse_time_str
Function: count_text
Function: count_image
Function: process

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/stream.py:
Import: cv2
Import: time
Import: yaml
Function: load_stream_config
Function: create_rtsp_url
Function: get_stream_capture
Function: get_frame

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/helpers.py:
Import: json
Import: yaml
Function: load_json
Function: load_yaml

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/metrics.py:
Import: time
Import: json
Import: functools
Import: types
Function: inference_metrics
  Description: Decorator to measure inference metrics:
      - Time to output first token.
      - Total inference time.
      - Tokens per second.
    Metrics are saved as JSON records appended to 'metrics.json'.
Function: wrapper
Function: generator_wrapper
Function: final_generator

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/video_utils.py:
Import: cv2
Import: os
From PIL import Image
Import: numpy
Function: load_video_frames
Function: standardize_frame
  Description: Standardize frame size to manage memory usage.

    Args:
        frames: List of frames (PIL Images or NumPy arrays)
        target_size: Target size for the longer dimension

    Returns:
        List of resized frames (as NumPy arrays) maintaining aspect ratio
Function: save_frame

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/utils/questions.py:
Import: json
Class: QuestionsUtils
Function: __init__
Function: load_json
Function: get_basic_questions
Function: get_basic_context
Function: get_basic_questions_with_context
Function: get_deconstructor_questions
Function: get_deconstructor_context
Function: get_deconstructor_questions_with_context
Function: get_constructor_actions

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/constructor/constructor.py:
From llms.lm_factory import LanguageModelFactory
Class: Constructor
Function: __init__
Function: execute

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/pipelines/pipeline_video_basic.py:
Import: os
Import: cv2
Import: time
Import: base64
Import: yaml
Import: json
From datetime import timedelta
From src.constructor.constructor import Constructor
From src.deconstructor.deconstructor_factory import DeconstructorFactory
From src.utils.helpers import load_json
From src.utils.video_utils import load_video_frames
From src.utils.yaml_utils import load_yaml
Function: load_config
Function: encode_frame
Function: initialize_deconstructor
Function: compute_context
Function: append_to_json_file
  Description: Appends data to a JSON file.
Function: format_timestamp
Function: run_pipeline

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/pipelines/pipeline_video_stream_lean.py:
Import: os
Import: re
Import: time
Import: json
Import: base64
Import: cv2
Import: numpy
From PIL import Image
From io import BytesIO
From datetime import timedelta
From src.constructor.constructor import Constructor
From src.deconstructor.deconstructor_factory import DeconstructorFactory
From src.utils.yaml_utils import load_yaml
From src.utils.helpers import load_json
From src.utils.video_utils import standardize_frame
From src.nx.nx_api import NxAPI
Import: time
Function: format_timestamp
  Description: Format seconds into HH:MM:SS format.
Function: encode_frame
  Description: Encode frame based on provider.
Function: get_activity_threshold
Function: clean_json_response
Function: append_to_alerts_file
Function: run_stream_pipeline
  Description: Process an RTSP stream by capturing one frame per second, converting the stream to frames,
    and then processing frames using the existing sliding window logic.
    
    In this pipeline:
      - Frames are captured at 1 frame per second.
      - The VLLM sliding window logic is used (e.g., 5 frames initially, adjusting window size).
      - When a constructor call is triggered (every 30 seconds with a 15-second step), the aggregated window-level
        analysis is passed to the constructor.
      - If the constructor output indicates suspicious activity, an event is sent to NX Witness via the NX API.
      - When the stream ends, the NX session is deleted.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/pipelines/pipeline_image_basic.py:
Import: yaml
Import: base64
From src.constructor.constructor import Constructor
From src.deconstructor.deconstructor import Deconstructor
From src.utils.helpers import load_json
From src.utils.helpers import load_yaml
Function: load_config
Function: run_pipeline

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/pipelines/pipeline_video_stream.py:
Import: os
Import: cv2
Import: time
Import: base64
Import: yaml
Import: json
Import: re
From src.constructor.constructor import Constructor
From src.deconstructor.deconstructor import Deconstructor
From src.utils.helpers import load_json
From src.utils.stream import get_stream_capture
From src.utils.stream import get_frame
From src.utils.stream import load_stream_config
From http.server import HTTPServer
From http.server import BaseHTTPRequestHandler
From threading import Thread
From src.utils.yaml_utils import load_yaml
Import: threading
Class: OutputHandler
Function: load_config
Function: encode_frame
Function: initialize_deconstructor
Function: compute_context
Function: compute_top_questions
Function: update_prompt
Function: run_pipeline
Function: do_GET
Function: set_data
Function: run_server

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/pipelines/pipeline_video_basic_lean.py:
Import: os
Import: re
Import: time
Import: json
Import: base64
Import: cv2
Import: numpy
From PIL import Image
From io import BytesIO
From datetime import timedelta
From src.constructor.constructor import Constructor
From src.deconstructor.deconstructor_factory import DeconstructorFactory
From src.utils.yaml_utils import load_yaml
From src.utils.helpers import load_json
From src.utils.video_utils import load_video_frames
From src.utils.video_utils import standardize_frame
Function: format_timestamp
  Description: Format seconds into HH:MM:SS format.
Function: encode_frame
  Description: Convert an image to the appropriate format for Hugging Face or Ollama.

    Args:
        frame: Image (PIL Image or NumPy array).
        provider: "huggingface" or "ollama" (passed from pipeline).

    Returns:
        - For Hugging Face: Returns a processed PIL Image.
        - For Ollama: Returns a Base64-encoded string.
Function: get_activity_threshold
  Description: Loads the JSON mapping file and returns the numeric threshold for the suspicious activity
    found in cleaned_chunk_analysis.
    
    Parameters:
      - cleaned_chunk_analysis: dict containing analysis results, for example:
          {
              "is_suspicious": "Yes",
              "suspicious_activity": "Detected shoplifting - concealed items, retail theft, or suspicious behavior in stores.",
              "level_of_suspicion": 4,
              "reasoning": "..."
          }
      - mapping_file_path: path to the JSON mapping file.
      
    Returns:
      - The numeric threshold for the activity if found in the mapping, else None.
Function: clean_json_response
Function: append_to_alerts_file
  Description: Append alert to the specified JSON file safely (using a temporary file).
Function: run_pipeline
  Description: Process a video in chunks and analyze each chunk using DeconstructorLean + Constructor.
    
    :param config_path: Path to pipeline configuration file.
    :param video_path: Path to input video.
    :param actions_path: Path to actions file.
    :param prompts_path: Path to prompts file.
    :param context_path: Path to context file.
    :param debug: Debug flag.
    :param chunk_duration: Duration (seconds) for each chunk.
    :param output_path: JSON file path to store alert results (e.g., suspicious chunks).
From datetime import timedelta
Import: traceback

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/anotator/annotator.py:
Import: pandas
From PIL import Image
From tqdm import tqdm
From src.utils.annotation import get_frame_at_time
From src.utils.annotation import standardize_frame
From src.utils.annotation import pil_to_base64
From src.utils.annotation import call_gpt4_vision
From src.utils.annotation import token_count_multi
From src.utils.annotation import parse_time_str
Function: annotate_clip_sliding
Function: process_csv_sliding

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/agents/agent_manager.py:
From pydantic import BaseModel
Class: AgentManager
Function: add_agent
Function: get_agent
Function: get_agents
Function: get_agent_names
Function: remove_agent

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/llms/base_lm.py:
From abc import ABC
From abc import abstractmethod
Class: BaseLanguageModel
Function: load_model
Function: predict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/llms/openai_lm.py:
From openai import OpenAI
From base_lm import BaseLanguageModel
Class: OpenaiLanguageModel
Function: __init__
Function: load_model
Function: predict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/llms/lm_factory.py:
Import: importlib
Class: LanguageModelFactory
Function: create_language_model

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/vllm_vlm.py:
From vllm import LLM
From vllm import SamplingParams
From vlm_interaction import BaseVLM
From PIL import Image
Import: numpy
From src.pipelines.pipeline_video_basic_lean import encode_frame
From typing import List
From typing import Dict
From typing import Any
Class: VllmVLM
Function: __init__
Function: load_model
Function: create_message
  Description: Create a message with frames and instruction for the model.
Function: sampling_params
  Description: Get sampling parameters for controlled output.
Function: predict
  Description: Make a prediction using the VLM.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/ollama_vlm.py:
Import: base64
Import: ollama
From vlm_interaction import BaseVLM
Class: OllamaVLM
Function: __init__
Function: load_model
Function: encode_image
Function: predict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/huggingface_vlm.py:
Import: base64
From io import BytesIO
From PIL import Image
Import: numpy
Import: torch
From transformers import AutoConfig
From transformers import AutoModelForCausalLM
From janus.models.processing_vlm import VLChatProcessor
From vlm_interaction import BaseVLM
Class: HuggingfaceVLM
Function: __init__
  Description: Initialize the HuggingfaceVLM with the specified model.
Function: load_model
  Description: Load and configure the model and processor.
Function: preprocess_image
  Description: Preprocess an image input to ensure it is in RGB format.
        Supports inputs as PIL.Image, bytes, or numpy.ndarray.
Function: predict
  Description: Generate predictions for the provided images and text prompt.
        
        Args:
            inputs: A tuple (frames, questions) where:
                - frames: List of image inputs.
                - questions: List of corresponding text prompts.
        
        Returns:
            A list of strings representing the generated responses.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/vlm_interaction.py:
From abc import ABC
From abc import abstractmethod
Class: BaseVLM
Function: load_model
Function: predict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus.py:
Import: os
Import: torch
Import: numpy
From transformers import AutoConfig
From transformers import AutoModelForCausalLM
From janus.models.processing_vlm import VLChatProcessor
From PIL import Image
Import: time
Function: preprocess_image
Function: load_model
Function: generate_response

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/vlm_factory.py:
Import: importlib
Class: VLMFactory
Function: create_vlm

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/__init__.py:
Import: sys
Import: collections
Import: collections.abc

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/__init__.py:
From image_processing_vlm import VLMImageProcessor
From modeling_vlm import MultiModalityCausalLM
From processing_vlm import VLChatProcessor

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/projector.py:
From typing import Tuple
From typing import Union
Import: torch
Import: torch.nn
From attrdict import AttrDict
Class: MlpProjector
Function: __init__
Function: forward
  Description: Args:
            x_or_tuple (Union[Tuple[torch.Tensor, torch.Tensor], torch.Tensor]:  if it is a tuple of torch.Tensor,
                then it comes from the hybrid vision encoder, and x = high_res_x, low_res_x);
                otherwise it is the feature from the single vision encoder.

        Returns:
            x (torch.Tensor): [b, s, c]

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/clip_encoder.py:
From typing import Dict
From typing import List
From typing import Literal
From typing import Optional
From typing import Tuple
From typing import Union
Import: torch
Import: torch.nn
Import: torchvision.transforms
From einops import rearrange
From janus.models.siglip_vit import create_siglip_vit
Class: CLIPVisionTower
Function: __init__
Function: build_vision_tower
Function: feature_select
Function: forward
  Description: Args:
            images (torch.Tensor): [b, 3, H, W]

        Returns:
            image_features (torch.Tensor): [b, n_patch, d]
From transformers import CLIPVisionModel

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/vq_model.py:
From dataclasses import dataclass
From dataclasses import field
From typing import List
Import: torch
Import: torch.nn
Import: torch.nn.functional
From functools import partial
Class: ModelArgs
Class: Encoder
Class: Decoder
Class: VectorQuantizer
Class: ResnetBlock
Class: AttnBlock
Function: nonlinearity
Function: Normalize
Class: Upsample
Class: Downsample
Function: compute_entropy_loss
Class: VQModel
Function: VQ_16
Function: __init__
Function: forward
Function: __init__
Function: last_layer
Function: forward
Function: __init__
Function: forward
Function: get_codebook_entry
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: encode
Function: decode
Function: decode_code
Function: forward

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/modeling_vlm.py:
Import: torch
From attrdict import AttrDict
From einops import rearrange
From transformers import AutoConfig
From transformers import AutoModelForCausalLM
From transformers import LlamaConfig
From transformers import LlamaForCausalLM
From transformers import PreTrainedModel
From transformers.configuration_utils import PretrainedConfig
From janus.models.clip_encoder import CLIPVisionTower
From janus.models.projector import MlpProjector
Class: vision_head
Function: model_name_to_cls
Class: VisionConfig
Class: AlignerConfig
Class: GenVisionConfig
Class: GenAlignerConfig
Class: GenHeadConfig
Class: MultiModalityConfig
Class: MultiModalityPreTrainedModel
Class: MultiModalityCausalLM
Function: __init__
Function: forward
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: prepare_inputs_embeds
  Description: Args:
            input_ids (torch.LongTensor): [b, T]
            pixel_values (torch.FloatTensor):   [b, n_images, 3, h, w]
            images_seq_mask (torch.BoolTensor): [b, T]
            images_emb_mask (torch.BoolTensor): [b, n_images, n_image_tokens]

            assert torch.sum(images_seq_mask) == torch.sum(images_emb_mask)

        Returns:
            input_embeds (torch.Tensor): [b, T, D]
Function: prepare_gen_img_embeds
From janus.models.vq_model import VQ_models

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/siglip_vit.py:
Import: math
Import: warnings
From dataclasses import dataclass
From functools import partial
From typing import Callable
From typing import Dict
From typing import Final
From typing import List
From typing import Literal
From typing import Optional
From typing import Sequence
From typing import Set
From typing import Tuple
From typing import Type
From typing import Union
Import: torch
Import: torch.nn
Import: torch.nn.functional
From timm.layers import AttentionPoolLatent
From timm.layers import DropPath
From timm.layers import LayerType
From timm.layers import Mlp
From timm.layers import PatchDropout
From timm.layers import PatchEmbed
From timm.layers import resample_abs_pos_embed
From timm.models._manipulate import checkpoint_seq
From timm.models._manipulate import named_apply
Function: _no_grad_trunc_normal_
Function: trunc_normal_
  Description: The original timm.models.layers.weight_init.trunc_normal_ can not handle bfloat16 yet, here we first
    convert the tensor to float32, apply the trunc_normal_() in float32, and then convert it back to its original dtype.
    Fills the input Tensor with values drawn from a truncated normal distribution. The values are effectively drawn
    from the normal distribution :math:`\mathcal{N}(\text{mean}, \text{std}^2)`
    with values outside :math:`[a, b]` redrawn until they are within
    the bounds. The method used for generating the random values works
    best when :math:`a \leq \text{mean} \leq b`.
    Args:
        tensor: an n-dimensional `torch.Tensor`
        mean: the mean of the normal distribution
        std: the standard deviation of the normal distribution
        a: the minimum cutoff value
        b: the maximum cutoff value
    Examples:
        >>> w = torch.empty(3, 5)
        >>> nn.init.trunc_normal_(w)
Function: init_weights
Function: init_weights_vit_timm
  Description: ViT weight initialization, original timm impl (for reproducibility)
Class: Attention
Class: LayerScale
Class: Block
Class: VisionTransformer
  Description: Vision Transformer

    A PyTorch impl of : `An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale`
        - https://arxiv.org/abs/2010.11929
Class: SigLIPVisionCfg
Function: create_siglip_vit
Function: norm_cdf
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
  Description: Args:
            img_size: Input image size.
            patch_size: Patch size.
            in_chans: Number of image input channels.
            num_classes: Mumber of classes for classification head.
            global_pool: Type of global pooling for final sequence (default: 'token').
            embed_dim: Transformer embedding dimension.
            depth: Depth of transformer.
            num_heads: Number of attention heads.
            mlp_ratio: Ratio of mlp hidden dim to embedding dim.
            qkv_bias: Enable bias for qkv projections if True.
            init_values: Layer-scale init values (layer-scale enabled if not None).
            class_token: Use class token.
            no_embed_class: Don't include position embeddings for class (or reg) tokens.
            reg_tokens: Number of register tokens.
            fc_norm: Pre head norm after pool (instead of before), if None, enabled when global_pool == 'avg'.
            drop_rate: Head dropout rate.
            pos_drop_rate: Position embedding dropout rate.
            attn_drop_rate: Attention dropout rate.
            drop_path_rate: Stochastic depth rate.
            weight_init: Weight initialization scheme.
            embed_layer: Patch embedding layer.
            norm_layer: Normalization layer.
            act_layer: MLP activation layer.
            block_fn: Transformer block layer.
Function: init_weights
Function: no_weight_decay
Function: group_matcher
Function: set_grad_checkpointing
Function: get_classifier
Function: reset_classifier
Function: _pos_embed
Function: _intermediate_layers
Function: get_intermediate_layers
  Description: Intermediate layer accessor (NOTE: This is a WIP experiment).
        Inspired by DINO / DINOv2 interface
Function: forward_features
Function: forward_head
Function: forward

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/image_processing_vlm.py:
From typing import List
From typing import Tuple
From typing import Union
Import: numpy
Import: torch
Import: torchvision
Import: torchvision.transforms.functional
From PIL import Image
From transformers import AutoImageProcessor
From transformers import PretrainedConfig
From transformers.image_processing_utils import BaseImageProcessor
From transformers.image_processing_utils import BatchFeature
From transformers.image_utils import to_numpy_array
From transformers.utils import logging
Function: expand2square
Class: VLMImageProcessorConfig
Class: VLMImageProcessor
Function: __init__
Function: __init__
Function: resize
  Description: Args:
            pil_img (PIL.Image): [H, W, 3] in PIL.Image in RGB

        Returns:
            x (np.ndarray): [3, self.image_size, self.image_size]
Function: preprocess
Function: default_shape

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/models/processing_vlm.py:
From dataclasses import dataclass
From typing import Dict
From typing import List
Import: torch
From PIL.Image import Image
From transformers import LlamaTokenizerFast
From transformers.processing_utils import ProcessorMixin
From janus.models.image_processing_vlm import VLMImageProcessor
From janus.utils.conversation import get_conv_template
Class: DictOutput
Class: VLChatProcessorOutput
Class: BatchedVLChatProcessorOutput
Class: VLChatProcessor
Function: keys
Function: __getitem__
Function: __setitem__
Function: __len__
Function: to
Function: __init__
Function: new_chat_template
Function: apply_sft_template_for_multi_turn_prompts
  Description: Applies the SFT template to conversation.

        An example of conversation:
        conversation = [
            {
                "role": "User",
                "content": "<image_placeholder> is Figure 1.
<image_placeholder> is Figure 2.
Which image is brighter?",
                "images": [
                    "./multi-images/attribute_comparison_1.png",
                    "./multi-images/attribute_comparison_2.png"
                ]
            },
            {
                "role": "Assistant",
                "content": ""
            }
        ]

        Args:
            conversations (List[Dict]): A conversation with a List of Dict[str, str] text.
            sft_format (str, optional): The format of the SFT template to use. Defaults to "deepseek".
            system_prompt (str, optional): The system prompt to use in the SFT template. Defaults to "".

        Returns:
            sft_prompt (str): The formatted text.
Function: image_token
Function: image_id
Function: image_start_id
Function: image_end_id
Function: image_start_token
Function: image_end_token
Function: pad_id
Function: add_image_token
  Description: Args:
            image_indices (List[int]): [index_0, index_1, ..., index_j]
            input_ids (torch.LongTensor): [N]

        Returns:
            input_ids (torch.LongTensor): [N + image tokens]
            num_image_tokens (torch.IntTensor): [n_images]
Function: process_one
  Description: Args:
            prompt (str): the formatted prompt;
            conversations (List[Dict]): conversations with a list of messages;
            images (List[ImageType]): the list of images;
            **kwargs:

        Returns:
            outputs (BaseProcessorOutput): the output of the processor,
                - input_ids (torch.LongTensor): [N + image tokens]
                - target_ids (torch.LongTensor): [N + image tokens]
                - images (torch.FloatTensor): [n_images, 3, H, W]
                - image_id (int): the id of the image token
                - num_image_tokens (List[int]): the number of image tokens
Function: __call__
  Description: Args:
            prompt (str): the formatted prompt;
            conversations (List[Dict]): conversations with a list of messages;
            images (List[ImageType]): the list of images;
            force_batchify (bool): force batchify the inputs;
            **kwargs:

        Returns:
            outputs (BaseProcessorOutput): the output of the processor,
                - input_ids (torch.LongTensor): [N + image tokens]
                - images (torch.FloatTensor): [n_images, 3, H, W]
                - image_id (int): the id of the image token
                - num_image_tokens (List[int]): the number of image tokens
Function: batchify
  Description: Preprocesses the inputs for multimodal inference.

        Args:
            prepare_list (List[VLChatProcessorOutput]): A list of VLChatProcessorOutput.

        Returns:
            BatchedVLChatProcessorOutput: A dictionary of the inputs to use for multimodal inference.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/utils/conversation.py:
Import: dataclasses
From enum import IntEnum
From enum import auto
From typing import Dict
From typing import List
Class: SeparatorStyle
  Description: Separator styles.
Class: Conversation
  Description: A class that manages prompt templates and keeps all conversation history.
Function: register_conv_template
  Description: Register a new conversation template.
Function: get_conv_template
  Description: Get a conversation template.
Function: get_prompt
  Description: Get the prompt for generation.
Function: get_prompt_for_current_round
  Description: Get current round formatted question prompt during sft training
Function: set_system_message
  Description: Set the system message.
Function: append_message
  Description: Append a new message.
Function: reset_message
  Description: Reset a new message.
Function: update_last_message
  Description: Update the last output.

        The last message is typically set to be None when constructing the prompt,
        so we need to update it in-place after getting the response from a model.
Function: to_gradio_chatbot
  Description: Convert the conversation to gradio chatbot format.
Function: to_openai_api_messages
  Description: Convert the conversation to OpenAI chat completion format.
Function: copy
Function: dict

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/utils/io.py:
Import: json
From typing import Dict
From typing import List
Import: PIL.Image
Import: torch
Import: base64
Import: io
From transformers import AutoModelForCausalLM
From janus.models import MultiModalityCausalLM
From janus.models import VLChatProcessor
Function: load_pretrained_model
Function: load_pil_images
  Description: Support file path or base64 images.

    Args:
        conversations (List[Dict[str, str]]): the conversations with a list of messages. An example is :
            [
                {
                    "role": "User",
                    "content": "<image_placeholder>
Extract all information from this image and convert them into markdown format.",
                    "images": ["./examples/table_datasets.png"]
                },
                {"role": "Assistant", "content": ""},
            ]

    Returns:
        pil_images (List[PIL.Image.Image]): the list of PIL images.
Function: load_json

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/__init__.py:
Import: sys
Import: collections
Import: collections.abc

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/__init__.py:
From image_processing_vlm import VLMImageProcessor
From modeling_vlm import MultiModalityCausalLM
From processing_vlm import VLChatProcessor

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/clip_encoder.py:
From typing import Dict
From typing import List
From typing import Literal
From typing import Optional
From typing import Tuple
From typing import Union
Import: torch
Import: torch.nn
Import: torchvision.transforms
From einops import rearrange
From janus.janusflow.models.siglip_vit import create_siglip_vit
Class: CLIPVisionTower
Function: __init__
Function: build_vision_tower
Function: feature_select
Function: forward
  Description: Args:
            images (torch.Tensor): [b, 3, H, W]

        Returns:
            image_features (torch.Tensor): [b, n_patch, d]
From transformers import CLIPVisionModel

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/modeling_vlm.py:
From attrdict import AttrDict
From einops import rearrange
Import: torch
From transformers.configuration_utils import PretrainedConfig
From transformers import AutoConfig
From transformers import AutoModelForCausalLM
From transformers import PreTrainedModel
From transformers import LlamaConfig
From transformers import LlamaForCausalLM
From transformers.models.llama.modeling_llama import LlamaRMSNorm
From janus.janusflow.models.clip_encoder import CLIPVisionTower
From janus.janusflow.models.uvit import ShallowUViTEncoder
From janus.janusflow.models.uvit import ShallowUViTDecoder
Import: torch.nn
Function: model_name_to_cls
Class: VisionUnderstandEncoderConfig
Class: VisionGenerationEncoderConfig
Class: VisionGenerationDecoderConfig
Class: MultiModalityConfig
Class: MultiModalityPreTrainedModel
Class: MultiModalityCausalLM
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: __init__
Function: prepare_inputs_embeds
  Description: Args:
            input_ids (torch.LongTensor): [b, T]
            pixel_values (torch.FloatTensor):   [b, n_images, 3, h, w]
            images_seq_mask (torch.BoolTensor): [b, T]
            images_emb_mask (torch.BoolTensor): [b, n_images, n_image_tokens]

            assert torch.sum(images_seq_mask) == torch.sum(images_emb_mask)

        Returns:
            input_embeds (torch.Tensor): [b, T, D]

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/uvit.py:
Import: math
Import: torch
Import: torch.nn
Import: torch.distributed
Import: torch.nn.functional
From typing import Optional
From typing import Tuple
From typing import Union
Import: numpy
Import: torchvision
Import: torchvision.utils
From diffusers.models.embeddings import Timesteps
From diffusers.models.embeddings import TimestepEmbedding
From transformers.models.llama.modeling_llama import LlamaRMSNorm
Class: ImageHead
Class: GlobalResponseNorm
Class: Downsample2D
  Description: A 2D downsampling layer with an optional convolution.

    Parameters:
        channels (`int`):
            number of channels in the inputs and outputs.
        use_conv (`bool`, default `False`):
            option to use a convolution.
        out_channels (`int`, optional):
            number of output channels. Defaults to `channels`.
        padding (`int`, default `1`):
            padding for the convolution.
        name (`str`, default `conv`):
            name of the downsampling 2D layer.
Class: Upsample2D
  Description: A 2D upsampling layer with an optional convolution.

    Parameters:
        channels (`int`):
            number of channels in the inputs and outputs.
        use_conv (`bool`, default `False`):
            option to use a convolution.
        use_conv_transpose (`bool`, default `False`):
            option to use a convolution transpose.
        out_channels (`int`, optional):
            number of output channels. Defaults to `channels`.
        name (`str`, default `conv`):
            name of the upsampling 2D layer.
Class: ConvNextBlock
Class: Patchify
Class: Unpatchify
Class: UVitBlock
Class: ShallowUViTEncoder
Class: ShallowUViTDecoder
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: get_num_extra_tensors
Function: forward
Function: __init__
Function: forward

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/siglip_vit.py:
Import: math
Import: warnings
From dataclasses import dataclass
From functools import partial
From typing import Callable
From typing import Dict
From typing import Final
From typing import List
From typing import Literal
From typing import Optional
From typing import Sequence
From typing import Set
From typing import Tuple
From typing import Type
From typing import Union
Import: torch
Import: torch.nn
Import: torch.nn.functional
From timm.layers import AttentionPoolLatent
From timm.layers import DropPath
From timm.layers import LayerType
From timm.layers import Mlp
From timm.layers import PatchDropout
From timm.layers import PatchEmbed
From timm.layers import resample_abs_pos_embed
From timm.models._manipulate import checkpoint_seq
From timm.models._manipulate import named_apply
Function: _no_grad_trunc_normal_
Function: trunc_normal_
  Description: The original timm.models.layers.weight_init.trunc_normal_ can not handle bfloat16 yet, here we first
    convert the tensor to float32, apply the trunc_normal_() in float32, and then convert it back to its original dtype.
    Fills the input Tensor with values drawn from a truncated normal distribution. The values are effectively drawn
    from the normal distribution :math:`\mathcal{N}(\text{mean}, \text{std}^2)`
    with values outside :math:`[a, b]` redrawn until they are within
    the bounds. The method used for generating the random values works
    best when :math:`a \leq \text{mean} \leq b`.
    Args:
        tensor: an n-dimensional `torch.Tensor`
        mean: the mean of the normal distribution
        std: the standard deviation of the normal distribution
        a: the minimum cutoff value
        b: the maximum cutoff value
    Examples:
        >>> w = torch.empty(3, 5)
        >>> nn.init.trunc_normal_(w)
Function: init_weights
Function: init_weights_vit_timm
  Description: ViT weight initialization, original timm impl (for reproducibility)
Class: Attention
Class: LayerScale
Class: Block
Class: VisionTransformer
  Description: Vision Transformer

    A PyTorch impl of : `An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale`
        - https://arxiv.org/abs/2010.11929
Class: SigLIPVisionCfg
Function: create_siglip_vit
Function: norm_cdf
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
Function: forward
Function: __init__
  Description: Args:
            img_size: Input image size.
            patch_size: Patch size.
            in_chans: Number of image input channels.
            num_classes: Mumber of classes for classification head.
            global_pool: Type of global pooling for final sequence (default: 'token').
            embed_dim: Transformer embedding dimension.
            depth: Depth of transformer.
            num_heads: Number of attention heads.
            mlp_ratio: Ratio of mlp hidden dim to embedding dim.
            qkv_bias: Enable bias for qkv projections if True.
            init_values: Layer-scale init values (layer-scale enabled if not None).
            class_token: Use class token.
            no_embed_class: Don't include position embeddings for class (or reg) tokens.
            reg_tokens: Number of register tokens.
            fc_norm: Pre head norm after pool (instead of before), if None, enabled when global_pool == 'avg'.
            drop_rate: Head dropout rate.
            pos_drop_rate: Position embedding dropout rate.
            attn_drop_rate: Attention dropout rate.
            drop_path_rate: Stochastic depth rate.
            weight_init: Weight initialization scheme.
            embed_layer: Patch embedding layer.
            norm_layer: Normalization layer.
            act_layer: MLP activation layer.
            block_fn: Transformer block layer.
Function: init_weights
Function: no_weight_decay
Function: group_matcher
Function: set_grad_checkpointing
Function: get_classifier
Function: reset_classifier
Function: _pos_embed
Function: _intermediate_layers
Function: get_intermediate_layers
  Description: Intermediate layer accessor (NOTE: This is a WIP experiment).
        Inspired by DINO / DINOv2 interface
Function: forward_features
Function: forward_head
Function: forward

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/image_processing_vlm.py:
From typing import List
From typing import Tuple
From typing import Union
Import: numpy
Import: torch
Import: torchvision
Import: torchvision.transforms.functional
From PIL import Image
From transformers import AutoImageProcessor
From transformers import PretrainedConfig
From transformers.image_processing_utils import BaseImageProcessor
From transformers.image_processing_utils import BatchFeature
From transformers.image_utils import to_numpy_array
From transformers.utils import logging
Function: expand2square
Class: VLMImageProcessorConfig
Class: VLMImageProcessor
Function: __init__
Function: __init__
Function: resize
  Description: Args:
            pil_img (PIL.Image): [H, W, 3] in PIL.Image in RGB

        Returns:
            x (np.ndarray): [3, self.image_size, self.image_size]
Function: preprocess
Function: default_shape

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/src/vlm/janus/janusflow/models/processing_vlm.py:
From dataclasses import dataclass
From typing import Dict
From typing import List
Import: torch
From PIL.Image import Image
From transformers import LlamaTokenizerFast
From transformers.processing_utils import ProcessorMixin
From janus.janusflow.models.image_processing_vlm import VLMImageProcessor
From janus.utils.conversation import get_conv_template
Class: DictOutput
Class: VLChatProcessorOutput
Class: BatchedVLChatProcessorOutput
Class: VLChatProcessor
Function: keys
Function: __getitem__
Function: __setitem__
Function: __len__
Function: to
Function: __init__
Function: new_chat_template
Function: apply_sft_template_for_multi_turn_prompts
  Description: Applies the SFT template to conversation.

        An example of conversation:
        conversation = [
            {
                "role": "User",
                "content": "<image_placeholder> is Figure 1.
<image_placeholder> is Figure 2.
Which image is brighter?",
                "images": [
                    "./multi-images/attribute_comparison_1.png",
                    "./multi-images/attribute_comparison_2.png"
                ]
            },
            {
                "role": "Assistant",
                "content": ""
            }
        ]

        Args:
            conversations (List[Dict]): A conversation with a List of Dict[str, str] text.
            sft_format (str, optional): The format of the SFT template to use. Defaults to "deepseek".
            system_prompt (str, optional): The system prompt to use in the SFT template. Defaults to "".

        Returns:
            sft_prompt (str): The formatted text.
Function: image_token
Function: image_id
Function: image_start_id
Function: image_end_id
Function: image_start_token
Function: image_end_token
Function: pad_id
Function: image_gen_id
Function: add_image_token
  Description: Args:
            image_indices (List[int]): [index_0, index_1, ..., index_j]
            input_ids (torch.LongTensor): [N]

        Returns:
            input_ids (torch.LongTensor): [N + image tokens]
            num_image_tokens (torch.IntTensor): [n_images]
Function: process_one
  Description: Args:
            prompt (str): the formatted prompt;
            conversations (List[Dict]): conversations with a list of messages;
            images (List[ImageType]): the list of images;
            **kwargs:

        Returns:
            outputs (BaseProcessorOutput): the output of the processor,
                - input_ids (torch.LongTensor): [N + image tokens]
                - target_ids (torch.LongTensor): [N + image tokens]
                - images (torch.FloatTensor): [n_images, 3, H, W]
                - image_id (int): the id of the image token
                - num_image_tokens (List[int]): the number of image tokens
Function: __call__
  Description: Args:
            prompt (str): the formatted prompt;
            conversations (List[Dict]): conversations with a list of messages;
            images (List[ImageType]): the list of images;
            force_batchify (bool): force batchify the inputs;
            **kwargs:

        Returns:
            outputs (BaseProcessorOutput): the output of the processor,
                - input_ids (torch.LongTensor): [N + image tokens]
                - images (torch.FloatTensor): [n_images, 3, H, W]
                - image_id (int): the id of the image token
                - num_image_tokens (List[int]): the number of image tokens
Function: batchify
  Description: Preprocesses the inputs for multimodal inference.

        Args:
            prepare_list (List[VLChatProcessorOutput]): A list of VLChatProcessorOutput.

        Returns:
            BatchedVLChatProcessorOutput: A dictionary of the inputs to use for multimodal inference.

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/demo/streamlit_basic.py:
Import: streamlit
Import: json
Import: cv2
From PIL import Image
Import: io
Import: base64
Function: load_alerts
Function: display_alert
Function: get_video_thumbnail
Function: main

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/demo/streamlit_token_viz.py:
Import: streamlit
Import: pandas
Import: altair
Import: os
Function: load_csv
Function: parse_time_str
Function: annotations_per_clip

/home/<USER>/rmidigudla/deconstructur_lean/lluminaai/demo/streamlit_live_basic.py:
Import: streamlit
Import: requests
Import: numpy
Import: time
Import: json
Import: re
Import: ast
Import: base64
Function: clean_json_string
Function: parse_json_safely
Function: display_analysis
Function: main