[{"input": "/SOLUTION/new_eval_data/explosion/explosion_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 3, "reasoning": "Frames 4-6 show a vehicle accident followed by smoke and explosion.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 3, "reasoning": "Collisions and crashes between cars at the intersection may indicate an explosion or related disruptive incident; frames 4 to 7 are suspicious.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 4, "reasoning": "Frame 9 shows a burning car on the road, indicating potential explosion or fire.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 00:00:08 to 00:00:11 and 00:00:31 show fire, smoke, explosion which indicate a high level of suspicious activity.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "The sequence of actions from frames 0 to 13 indicates multiple explosions and fires in a building, showing a very high level of danger and suspiciousness", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames from timestamp 00:00:00 to 00:00:24 involve multiple car crashes and an explosion accident at 00:00:23, indicative of possible dangerous activities.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 4, "reasoning": "Frames 2, 8, 9, 12, 16, 17, 18, 23, 26, 27, 29, 35, 39, 40, 41, 42, 46 indicate flames, smoke, or fire, suggesting an explosion or fire incident.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:00 to 00:00:28 indicate repeated explosions, smoking, and fire in a building, indicating a highly suspicious event.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 0 to 6 and 9 to 10 show actions consistent with an explosion, including shooting fire, burning vehicles, explosion fire smoke, and smoking vehicles/trucks.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_10.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 5, "reasoning": "Multiple explosions and fire detected between frames 4 to 30, indicating a highly suspicious situation.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_11.mp4", "output": {"is_suspicious": "may be", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 2, "reasoning": "Frames from 00:00:00 to 00:00:03 show a fire hydrant leaking and spraying water on the street, which may indicate damage or tampering, potentially linking to vandalism.", "class_number": 5}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 4 to 5 show smoke and a vehicle on fire, indicating a potential explosion or fire hazard.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:14 to 00:00:19 describe fire, explosion, and flaming wreckage, indicating a serious explosion incident.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Explosion and smoke detected between frames 29-32, followed by another explosion at frame 47, indicating a potentially dangerous situation.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_16.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious activities such as robbery occurred at timestamp 00:00:00, and theft activities detected in frames 00:00:24.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 3, "reasoning": "Frame 9: Car failed to stop for pedestrians at crosswalk indicating possible threatening behavior; Frame 23: Vehicle crashing into building suggesting possible forced entry or accident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 4 to 23 show various fire, explosion, smoke incidents indicating a severe situation.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Multiple frames (2, 23, 25, 26) indicate an explosion with smoking and exploding batteries, posing a high level of danger.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames 4, 19, 20, and 24 show physical altercations, indicating repeated violent behavior.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_23.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frame 37 indicates an explosion, and frames 38 show looting, both are highly suspicious.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_25.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 4, "reasoning": "Frames 4 and 8 show smoking car and car engine fire, indicating a potential explosion or fire hazard.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 3, "reasoning": "Smoke coming from truck detected at frame 22, indicating a potential fire or explosion.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_28.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected robbery incident, Detect physical altercation, Detected explosion", "level_of_suspicion": 5, "reasoning": "Timestamp 00:00:14 shows robbing bank cash register indicating a robbery, Timestamp 00:00:29 shows fighting indicating a physical altercation, and Timestamps 00:00:49 to 00:01:00 shows smoke and a car crash indicating a potential explosion or vehicle fire.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/explosion/explosion_30.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Suspicious activity detected at frame 7 due to fighting people", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_1.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activity detected in the provided frames; all actions are marked as No Action.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_2.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "All frames show no action detected, hence no suspicious activity is observed.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 3, "reasoning": "The action detected at timestamp 00:00:02 indicates shoplifting, which is a suspicious individual action.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "The frame at timestamp 00:00:02 indicates shoplifting which is a suspicious activity.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "The sequence from frame 4 to frame 7 indicates a person falling and lying on the ground, suggesting a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "Frames 4 and 8 indicate theft and car stealing activities, which are suspicious actions.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 4, "reasoning": "Frames from 00:00:03 to 00:00:05 show a person punching and vandalizing cars.", "class_number": 5}}, {"input": "/SOLUTION/new_eval_data/falling/falling_9.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "All frames indicate no action, observation in the lobby does not imply suspicious behavior.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "There are no actions detected that align with any suspicious activity from the list.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames at timestamp 00:00:06 show punches being thrown, indicating a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frame 1 indicates an act of punching, followed by frame 2 where someone fell down, suggesting a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_14.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "No input data provided to determine any suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "Frames 0 to 5 indicate falling and lying down, suggesting a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_16.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "No actions detected in any frames.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 2 and 4 are suspicious indicating stealing and robbery at a cash register.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 3, "reasoning": "Frame 3 indicates a shoplifting incident which is a suspicious individual action involving stealing.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frame 00:00:05 shows shoplifting, indicating potential theft inside store making it a burglary act", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/falling/falling_23.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frame 0 shows an action of hitting a person, which indicates a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_24.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential robbery incident", "level_of_suspicion": 3, "reasoning": "Frames 3-4 show fleeing from a store which could indicate a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 4 and 7 are suspicious indicating theft and robbery respectively, both requiring immediate attention.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_29.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "Frame at timestamp 00:00:09 shows a person lying on the floor in a store aisle, which may indicate a health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/Fighting/Vandalism_7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Continuous fighting and punching actions observed from timestamps 00:00:00 to 00:00:09 indicate a serious physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/Fighting/Vandalism_9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Frames 00:00:03, 00:00:17, 00:00:24, 00:00:26, 00:00:29, 00:00:33, 00:00:34, and 00:00:35 indicate fighting, brawl escalation, and a reported death.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_0.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 1, 2, 4, and 8 show robbery and assault, indicating a robbery incident with physical violence.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:02, 00:00:03, 00:00:34, 00:00:37, 00:00:38 indicating a high suspicion of physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 4, "reasoning": "Frames 6 and 10 indicate a robbery and car robbery, using forceful or threatening means.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/temp_4.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "No suspicious activities detected in the provided input.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/temp_8.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No context, questions, or answers provided to analyze for suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_10.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame at timestamp 00:00:01 indicates a robbery in progress which is a clear threat.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 10, 19, 22, and 23 indicate shoplifting, stealing, and potential robbery incidents suggesting threatening behavior.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "Multiple instances of people falling or lying on the floor detected in frames: 21-22, 25-26, 35, 40, 54, 61-62, 69, 75, 84, 92, 99, 104, 109, 112, 114, indicating potential health incidents.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Robbery activities noted at timestamps 00:00:00, 00:00:03, and cash register robbing at 00:00:04 indicate a robbery in progress.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames 16 and 19 indicate a physical altercation or fighting detected at a park street.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_24.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 4, "reasoning": "Frames 9 and 18 indicate acts of vandalism in the vehicle lot area", "class_number": 5}}, {"input": "/SOLUTION/new_eval_data/Fighting/temp_25.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No relevant data provided for analysis.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/Fighting_26.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 3, "reasoning": "Frame range 00:00:06 to 00:00:07 indicates a bump into a man followed by a fight which is suspicious.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/Fighting/temp_31.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No data provided to assess any suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/Fighting/temp_32.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No observations were provided to assess suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 3, "reasoning": "Frame 2 shows evidence of physical altercation - Punching person, which indicates potential violence.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame 3 indicates a robbery and Frame 5 is suspicious for theft, both involving potential robbery behavior.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 2, "reasoning": "Frame 4 shows a man falling down stairs which could indicate a potential health incident", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:04 and 00:00:09 are suspicious due to a man pointing a gun and a shoplifting incident respectively.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_7.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activity detected in all observed frames from 00:00:00 to 00:00:04.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "The frames 00:00:04 to 00:00:07 show an individual falling and then laying/lying on the floor, which could indicate a health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 3, "reasoning": "Frames 3 to 13 are suspicious due to actions like bumping car, hitting van, facing van, and bending over van, indicating potential theft or vandalism.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frame 5 shows unauthorized entry, and frame 4 indicates car theft, suggesting a possible burglary incident.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/falling/falling_9.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "All actions observed are either No Action or Cleaning floor, no suspicious activities detected in the given frames.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "Only normal activity (Man walking in room) observed at timestamp 00:00:01", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 3, "reasoning": "Frame at timestamp 00:00:09 indicates shoplifting in customer aisle.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 2, "reasoning": "Frame at timestamp 00:00:06 shows a person falling, which may indicate a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 2, "reasoning": "Frame 4 shows a person collapsing on the floor, suggesting a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_14.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activity detected within the provided context and data.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "Suspicious activity detected in frames 0, 2, and 4 where a man falls to the ground and lies on the floor, indicating a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of stealing activities observed including stealing shopping cart and items between frames 00:00:02 to 00:00:08", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_21.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activity detected in the provided frames.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_30.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:01 and 00:00:04 show running in factory followed by robbing money machine indicating a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frame 5 shows brawling in store which is a physical altercation, thus it is suspicious", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_23.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "There are no suspicious activities detected within the recorded frames.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 3, "reasoning": "Frame 8 indicates fighting, which could be a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/falling/falling_29.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "Frame at timestamp 00:00:01 shows suspicious activity of stealing alcohol.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_0.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 4, "reasoning": "Frames 25 to 29, 129, 131, and 134 show spray painting and graffiti, indicating vandalism activity.", "class_number": 5}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:41, 00:01:02, 00:01:23, which indicates recurring physical altercations.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "Frames 7 to 16 show actions such as opening doors and a person attempting unauthorized entry, indicating possible attempted break-in.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 159 and 314 indicate robbing bank vault and ATM, suggesting a high-level threat of robbery.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 4, "reasoning": "Frames 8 - 32 show a fire burning in parking lot followed by smoke in the air, indicating a potential explosion or fire incident.", "class_number": 6}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_5.mp4", "output": {"is_suspicious": "may be", "suspicious_activity": "Detect suspicious individual actions", "level_of_suspicion": 2, "reasoning": "Running at timestamp 00:00:03 and chasing at 00:00:04 could indicate a suspicious action, but insufficient context for a specific threat.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of unattended luggage from frames 10-40, including explicit mentions of abandoned and unattended luggage.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames from timestamp 00:00:13 to 00:00:15 show escape from elevator, punching, and fighting, indicating potential robbery with forced theft and threatening behavior.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of suspicious actions related to baggage such as opening unattended luggage, stealing luggage, and leaving bags unattended observed in frames (8-10, 49-51, 272-273, 348-354, 374-375, 439-440).", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames from 00:00:24 to 00:00:35 indicate a physical altercation, including a punch in face and fighting.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Frames between 00:00:04 and 00:00:09 show theft at cashier and laptop counters which indicates potential burglary activity.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/temp_22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 3, "reasoning": "Frames 16, 47, 49, 142, 143 indicate bags left unattended, while frame 11 reports lost luggage, suggesting suspicious activity.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "The observed actions were running, walking, and sitting on a bench; these are typical behaviors in a sidewalk context. No suspicious actions like theft, altercations, or unauthorized activities were detected.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "Frames 73 and 134 indicate theft and stealing, which are suspicious activities in a classroom setting.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected atypical group behaviors - forming group", "level_of_suspicion": 2, "reasoning": "Frame 44 shows a crowd forming on the sidewalk which could indicate a gathering of interest or concern.", "class_number": 2}}, {"input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames 44-49 indicate physical altercation and pushing in a hallway.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions", "level_of_suspicion": 4, "reasoning": "Unattended luggage detected at timestamps 00:00:08, 00:00:09, 00:00:16, and 00:02:30 indicating potential threat.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 3, "reasoning": "Action of unlocking door (timestamp 00:00:08) followed by taking paper card reader (timestamp 00:00:09) indicates potential unauthorized access.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/falling/falling_1.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "All frames show no action indicating any suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 3 to 4 show actions related to robbery and stealing items, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "Frame at timestamp 00:00:01 indicates stealing a package", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 4, "reasoning": "Frames with timestamps 00:00:00 (Shoplifting) and 00:00:09 (Theft walking aisle) indicate suspicious activities related to theft.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "Frame 1 indicates shoplifting, Frame 4 indicates shopping cart stealing, both are forms of stealing", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/falling/falling_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential health incidents - fainting/falling/firstaid", "level_of_suspicion": 3, "reasoning": "Frames 5 to 7 indicate a person fell down, is lying on the floor, and is described as a falling person, which suggests a potential health incident.", "class_number": 3}}, {"input": "/SOLUTION/new_eval_data/falling/falling_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect potential robbery incident - weapon use, forced theft, mugging, covering face, threatening behavior, snatching valuables", "level_of_suspicion": 5, "reasoning": "Frame 15 indicates a robbery in progress, which is suspicious and high alert activity.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/falling/falling_8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frames 00:00:02 to 00:00:06 show actions including lifting the hood of a car, climbing the car, robbing a car, and theft, which are indicative of a burglary in progress.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/falling/falling_9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access", "level_of_suspicion": 3, "reasoning": "The individual was prowling in the lobby at timestamp 00:00:04, indicating potential unauthorized access.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/falling/falling_10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "No suspicious activity detected in any of the frames provided.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_0.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 4 to 9 involve robbery actions indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 3 to 5 show pointing a gun and shooting a car driver, followed by frames 24 to 25 involving car chase and taking car. Frame 37 shows fleeing the scene, indicating a robbery.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious activities observed at frames 2 (Jumping fence), 4 (Robbery store), 14 (<PERSON><PERSON> cash register), indicating potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple instances of stealing and robbery observed between frames 14 and 44.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "Theft actions detected at timestamps 00:00:00 and 00:00:16 along with suspicious activity such as stealing car keys at 00:00:24 indicate potential unauthorized access or theft.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - stealing", "level_of_suspicion": 4, "reasoning": "Frame at timestamp 00:00:09 indicates stealing a backpack in a car park, which is a suspicious individual action.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious activity detected at timestamp 00:00:11 with an action labelled as robbery; additional suspicious action of shooting gun road at 00:00:14 supports high level of suspicion.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 23 to 24 and 44 indicate possible robbery and break-in activities involving vehicles in a driveway and garage.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 5, 14, 24, 29 indicate ongoing robberies or theft related incidents.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Frames 26 (<PERSON> climbing out of car window), 29 (Stealing car keys <front yard>), 42 (<PERSON><PERSON> car), and 55 (Robbing car) indicate suspicious activities related to a potential burglary incident involving theft of and from cars.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_16.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 3, "reasoning": "Frames from 00:00:04 (Vandalism parked car) and 00:00:08 (<PERSON> dragging car) indicate suspicious actions involving the parked car.", "class_number": 5}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames with timestamps 00:00:02 and 00:00:19 indicate robbery actions, including robbery of cash register, suggesting a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/robbery_20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames from timestamp 00:00:00 to 00:01:31 indicate multiple instances of theft, breaking in, robbing, and taking items from cash registers, qualifying as distinct burglary actions.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/Rob<PERSON>_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "The frames show continuous suspicious activities such as armed robbery at 00:00:12, stealing items at 00:00:36, robbing store merchandise at 00:00:49, and multiple instances of robbery from 00:01:10 to 00:01:52, indicating a serious robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/Rob<PERSON>_3.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No data provided to analyze for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Multiple frames indicate burglary and forced entry actions like robbing jewelry store (frame 9), theft (frame 32), robbery display case (frame 39), breaking door (frame 57), and robbery (frame 60).", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Multiple instances of physical altercation observed at timestamps 00:00:03, 00:00:04, 00:00:14, 00:00:25, 00:00:27, and 00:00:28 indicate ongoing fighting.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_6.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activities detected based on the provided questions and answers.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 13 to 44 indicate a sequence of suspicious actions including a man holding a knife, stealing money, shoplifting, and robbery of a cash register, potentially involving weapon use and forced theft.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_9.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 0, "reasoning": "The provided context, questions, and answers are empty.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "none", "level_of_suspicion": 1, "reasoning": "No context, questions, or answers provided to analyze for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:11 to 00:00:24 include attacking and robbing a car, indicating high suspicion for robbery.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "Climbing truck (00:00:06), climbing fence (00:00:46) could indicate unauthorized access or attempted burglary.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious activities such as Drive by shooting (frame 1), Stealing bags (frame 9), Robbery car driveway (frames 24 and 34), Theft of car (frame 37), Stealing car driveway (frame 39), Vandalize car driveway (frame 44), and Stealing car keys driveway (frame 64) indicate a high level of suspicious activity involving robbery and theft.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 5, "reasoning": "Actions indicating stealing or theft were observed at frames 4, 16, and 23, suggesting continuous suspicious activities.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Suspicious activities include looking through window at 00:00:03, car being broken into at 00:00:31, theft from car door at 00:00:34, climbing fence at 00:00:35-00:00:41, and robbing car at 00:00:53.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Robbery cash register at frame 9 indicates a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_19.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activities detected in the given context.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_20.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "There are no observations provided to analyze for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames 4 (<PERSON><PERSON><PERSON> money machine), 5 (Climbing window), 22 (<PERSON><PERSON>), 24 (Break in store door), 28 (Break Window), 48 (Smashing window), and 58 (Climbing wall) indicate attempted break-in and theft with potential structural damage.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames 147 (Shooting person), 194 (Stealing clothes line), 459 (Break-in door), and 995 (Theft) show activities indicating burglary with unauthorized access and theft.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_23.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames in range 00:00:24 to 00:00:36, 00:02:44, 00:03:29, 00:07:35, 00:09:13, 00:09:23, 00:12:44, 00:13:19, 00:14:53, 00:15:49, 00:16:56, 00:19:54 involve robberies, including weapon use and forced entry actions.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_24.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Numerous instances of robbing and stealing actions detected at various timestamps including 00:00:02, 00:00:03, 00:00:13, 00:00:19, 00:00:34, 00:00:43, 00:01:14, 00:01:46, suggesting a high level of suspicious activity related to robbery.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_25.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "N/A", "level_of_suspicion": 1, "reasoning": "No context or scene descriptions were provided for evaluation", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_26.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No context, questions or answers provided to assess suspicion.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple frames including timestamps 00:00:18, 00:00:34, 00:00:44, 00:00:50, 00:00:54, 00:01:00, 00:01:03, 00:01:32 indicate robbery activities including robbing and stealing actions.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_28.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 0, "reasoning": "No context, questions, or answers were provided to analyze for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_29.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple frames (e.g., 2, 4, 7, 23, 36, 39, 45, 50) indicate actions such as robbing store, theft, and robbery attempts, which are strong indications of a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_30.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 15 to 24 show robbing a car, stealing a car, and shooting a gun in a parking lot, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_31.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames such as 00:00:04, 00:00:09, 00:00:34, 00:00:44, 00:01:12, 00:03:14 indicate repeated instances of robbery actions.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_32.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Multiple instances of climbing walls and forcing entry at timestamps 00:01:48, 00:02:29, 00:07:21, 00:09:23, indicating attempted break-ins or unauthorized access.", "class_number": 7}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_33.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 4, 9, 10 depict a robbery with potential weapon use and threatening behavior.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_34.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing", "level_of_suspicion": 3, "reasoning": "Frame 12 shows a person walking away from luggage, which could indicate an unattended bag.", "class_number": 4}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_35.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple frames (17, 19, 24, 27, 28, 29, 30, 33) indicate fighting behavior, indicating a serious physical altercation is occurring.", "class_number": 1}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_37.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 3-4 and 13-14 show a chasing incident and the presence of a gun, indicating a potential robbery.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_38.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 4, "reasoning": "Frames 2 to 6 show fighting and robbing on the street followed by chasing, indicating a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/new_eval_data/rob/<PERSON><PERSON>_39.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame 11 detected pointing gun at people and frame 12 detected fighting person, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/robbery/Rob<PERSON>_28.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 0, "reasoning": "The context, questions, and answers provided are empty, thus no suspicious activity can be assessed.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/vandalism/Vandalism_87.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple instances of robbery-related actions detected at timestamps 00:00:08, 00:00:34, 00:00:49, 00:00:53, 00:01:23.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/vandalism/Monday Morning Sept 2nd 2024.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Suspicious activities include jumping over wall (frame 5), theft graffiti wall (frame 9), man breaking window (frame 34), forced entry (frame 41), smashing window (multiple instances), and stealing (frames 182 and 198), indicating potential burglary with unauthorized access and structural damage.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/vandalism/PART 1 ATM THIEF gone it 10 minutes or less.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, stealing; Detect vandalism - breaking objects", "level_of_suspicion": 4, "reasoning": "The frames between 00:00:04 to 00:04:38 include multiple instances of theft, vandalism, and suspicious behavior such as stealing from vehicles, vandalizing property, and breaking window storefronts.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/Attempted Robbery Duped by <PERSON><PERSON> Doors ｜｜ ViralHog.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple frames indicate robbery activity: frames 4, 19, 24, 30, 44, 66, 79, 84, 90, 93, 94.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Multiple instances of theft, shoplifting, and robbery detected between frames 18-134.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames with timestamps 00:00:56, 00:00:59, and 00:01:39 indicate robbery activities at cashier checkout and cash register.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, hiding baggage, unattended bags", "level_of_suspicion": 5, "reasoning": "Multiple instances of shoplifting detected at frames 0, 25, 38, and 86; luggage left alone at frame 53; and items being stolen/hidden at frames 59, 66, 68, 69, and 95.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:04, 00:00:29, and 00:01:45, indicating possible violence.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect vandalism - breaking objects", "level_of_suspicion": 3, "reasoning": "Frame 29 shows graffiti being painted on a wall which is an act of vandalism.", "class_number": 5}}, {"input": "/SOLUTION/Demo_videos/other/Caught thief stealing wine bottles.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, hiding baggage, unattended bags", "level_of_suspicion": 5, "reasoning": "Repeated instances of shoplifting and theft detected in frames ranging from 00:00:01 to 00:02:10.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/fighting_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple timestamps indicate fighting and violent actions, such as at 00:00:01, 00:00:21, 00:01:11, and 00:01:23.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Frames 6 to 10 indicate fighting behavior, potentially involving multiple individuals.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:07, 00:00:21, 00:00:23, 00:00:25, 00:00:27, 00:00:35, 00:00:37, 00:00:39, 00:00:42, indicating continuous violent activity.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:04, 00:00:10, 00:00:19, 00:00:24, 00:00:25, 00:00:34, and 00:00:37 indicate shoplifting and robbery activities suggesting a series of thefts or similar incidents.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple frames indicate robbery actions (timestamps: 00:00:02, 00:00:04, 00:00:34) along with theft and shoplifting activities (timestamps: 00:00:09, 00:00:14, 00:00:39), indicating a high level of suspicion of a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious activities detected include robbery (timestamps 00:00:29, 00:00:44, 00:01:49, 00:02:29) and use of a weapon (timestamp 00:02:30), indicating a high risk of robbery.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames 1, 9, 12, and 29 indicate physical altercation with actions such as fighting, pushing, and hitting.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/25.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frames 9 and 45 indicate theft activity, frame 50 describes a person being dragged from a vehicle, indicative of robbery or violent altercation.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/26.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Frames 6, 7, and 9 indicate fighting behavior in the street, suggesting a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple instances of fighting and physical altercation observed at timestamps 00:00:05, 00:00:07, 00:00:09, 00:00:14, 00:00:17, 00:00:18, 00:00:19, 00:00:20, 00:00:21, 00:00:44, suggesting a high level of violence.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/31.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple timestamps (00:00:02, 00:00:05, 00:00:08, 00:00:20, 00:00:22, 00:00:31, 00:00:34, 00:00:35, 00:00:39, 00:00:46, 00:00:49, 00:00:55, 00:00:56, 00:01:01, 00:01:04) indicate fighting or physical altercation, suggesting ongoing violent behavior.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 3, "reasoning": "Several instances of unattended, abandoned, or left luggage from frames 00:00:04 to 00:00:40.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/output_clip.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 4, "reasoning": "Frames 81 and 100 indicate a possible bike theft while frame 129 confirms a bicycle stolen on the sidewalk.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident - weapon use, forced theft, mugging, covering face, threatening behavior, snatching valuables", "level_of_suspicion": 5, "reasoning": "Frames such as 05, 34, 46, 54, 104, and 129 indicate activities like hiding face, robbery, looting, and shooting, indicating a high level of threat and suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting", "level_of_suspicion": 4, "reasoning": "Frame 19 indicates shoplifting activity in a store setting.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "The activities of climbing wall at multiple timestamps (00:00:03, 00:00:55, 00:02:25, 00:05:47, 00:06:46, 00:07:23) indicate potential unauthorized access attempts.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frames between 00:00:05 and 00:03:26 show hacking, unauthorized access, and suspicious behavior related to potential burglary.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Unauthorized Access, Theft", "level_of_suspicion": 5, "reasoning": "Stealing bag store at 00:00:14, climbing wall at 00:00:23, man running security terminal at 00:00:24, indicate unauthorized access and potential theft.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 4, "reasoning": "Frames 28 to 29 show cutting wire and vacuum suctioning electrical cord, indicating possible unauthorized access or tampering with electronics.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "The actions observed such as lifting bag and sitting chair do not fall under any suspicious categories.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/explosion/unattented_bags_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Fight observed at frames 9, 15, 16, 51, 61, 90, 153.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 4, "reasoning": "Frames 11 (Smoking vehicle), 12 (Car crash), 24 (Smoke car fire), 49 (Smoke from car), 54 (Smoke haze sky), 79 (Smoke explosion bridge), 86 (Fire on hillside), 103 (Smoke billowing from car), 107 (Smoke emitting from vehicle) indicate multiple instances of smoke, fire, and possible explosions.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Activities including opening door, broken door, breaking door, and smashing window observed at frames 15, 26, 42, and 47 demonstrate unauthorized access and structural damage.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags, stealing", "level_of_suspicion": 5, "reasoning": "Frames 44-46 indicate robbing and stealing activities. Additionally, multiple frames from 48 onwards show unattended bags and unauthorized baggage actions.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame at timestamp 00:00:09 indicates a robbery car park activity, suggesting a robbery in progress.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/4.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No data provided; cannot assess any suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 4, "reasoning": "Abandoned luggage detected at multiple timestamps like 00:00:02, 00:03:29 and again observed at frame 00:04:59 indicating unattended baggage which is highly concerning.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 3, "reasoning": "Frame 34 shows bags alone on a bench, indicating unattended baggage.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/7.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 0, "reasoning": "No context, questions, or answers provided for analysis.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 3, "reasoning": "Running in the office at timestamp 00:00:13, followed by abandonment detected in empty office at 00:00:19 and 00:00:21, indicates potential unauthorized access.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detections of suspicious individual actions - unattended bags, stealing", "level_of_suspicion": 4, "reasoning": "Frames from 9 (Theft of equipment) & 14 and 163 (Abandoned package) indicate suspicious actions related to stealing and unattended bags", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/10.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "N/A", "level_of_suspicion": 1, "reasoning": "No context, questions, or answers were provided to analyze for suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags", "level_of_suspicion": 3, "reasoning": "Frames 52 to 83 show a man walking away from a package, and Frame 83 indicates a package left on the street, which is considered suspicious.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 3, "reasoning": "Frames 9 and 39 indicate physical altercations near an elevator and escalator entrances respectively, which are suspicious behaviors indicating possible workplace violence.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/15.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No context or questions/answers provided to assess suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/16.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 1, "reasoning": "No data provided to assess for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 3, "reasoning": "Frames 00:01:36 to 00:01:37 show a person leaving luggage, which is suspicious in a station environment.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Robbing store at frame 9 and stealing backpack on sidewalk at frame 49 indicate potential robbery incidents.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Frames 14 to 20 indicate theft and shoplifting activities.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 3, "reasoning": "Frames 8, 9, 19, 50, 53, 55, 349, 353, 359, and 357 indicate unattended luggage or bags left alone in potentially sensitive locations such as airports or public transport areas.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/21.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "The observed actions such as running and climbing in a classroom setting do not align with any predefined suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions", "level_of_suspicion": 4, "reasoning": "Frames 22 to 24 indicate abandoned objects, and frames 74 to 81 show luggage being left unattended in a train station indicating potential suspicious action.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/23.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious activities were detected in the provided context.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/24.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion", "level_of_suspicion": 5, "reasoning": "Suspicious activities detected between frames 3 to 5 indicating explosions and criminal activity.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other/25.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "All frames indicate no action taking place.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/27.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Suspicious activities detected including running away (00:00:05), robbing store (00:00:06), breaking window (00:00:07 and 00:00:10), and breaking into car trunk (00:00:14), indicating a burglary incident.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/28.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No suspicious behavior or activity detected in the observations provided.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/29.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - unattended bags", "level_of_suspicion": 4, "reasoning": "Multiple instances of luggage being abandoned or unattended detected at frames 1, 5, 9, 11, 14, 17, 18, 19, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/31.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "No data was provided to analyze for suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/32.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions", "level_of_suspicion": 3, "reasoning": "Unattended luggage observed from frames 00:00:19 to 00:00:29 including street and bus stop locations.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/33.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 2, "reasoning": "A man was observed running in a train station from frame 8 to frame 24 which is typically monitored closely for possible threats or suspicious behavior.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/34.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 3, "reasoning": "Multiple instances of unattended bags are detected from timestamps 00:00:02 to 00:00:59.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/35.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "None", "level_of_suspicion": 1, "reasoning": "The sequence of actions does not indicate any suspicious activity from the given list.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/36.mp4", "output": {"is_suspicious": "No", "suspicious_activity": "", "level_of_suspicion": 0, "reasoning": "No context, questions, and answers provided to determine suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/37.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "The man is carrying a large gun at timestamp 00:00:05; This is immediately followed by an action indicating a person stealing an item at timestamp 00:00:14.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/38.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions -  hiding baggage, unattended bags", "level_of_suspicion": 5, "reasoning": "Frames 19 to 35 show unattended luggage, a suspicious bag in a station, indicating potential security threat.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/39.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Actions of climbing wall (frame 113) and stealing suitcase at airport (frame 139) indicate potential unauthorized access and theft.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:29 to 00:00:41 indicate robbing bank by motorcycle gang with actions like robbing, climbing wall, and driving fast.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame 9 shows a robbery happening in front of the hotel which is a confirmed suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Frames 109 and 122 indicate luggage abandoned which is suspicious.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame 59 shows a person stabbing a woman, and frame 64 indicates stealing a bag from a woman. These actions indicate a violent robbery.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of luggage being abandoned are detected, specifically at frames 20 and 125, along with walking away from luggage at frames 46 and 116.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/Unattended_bags_11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:31, 00:00:36, 00:00:40, 00:00:41, 00:00:45, and 00:00:52 indicate potential workplace violence.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Multiple incidents of shoplifting and theft detected at timestamps 00:00:04, 00:00:21, and 00:00:34.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 4 and 13 show threatening behavior with a weapon, indicating potential robbery.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Robbing activities at timestamp 00:00:09 and 00:00:27, punch thrown at 00:00:14 indicates a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 3, "reasoning": "Frames with timestamps from 00:00:19 to 00:01:02 indicate fighting behavior.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:19 to 00:00:29 indicate fighting and brawl in the hallway, which is a clear indicator of workplace violence.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:13 to 00:00:49 indicate multiple robbery-related actions such as robbery shelves, theft, and theft of pistol, as well as violence including fighting, assault and shoot-out.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Frames 17, 19, 44, and 45 indicate punching and fighting actions, suggesting a physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/16.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 29, 45, 49, 94, 110, 111, 147, 148 show theft and robbery actions, indicating ongoing robbery activities.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other1/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames 10, 37, 44, 46, 56, 62, 69, 71, 79, and 81 show unauthorized access, stealing, and breaking objects indicating possible burglary.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other1/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frames 2 (Hacking website) and 29 (Theft couch store) indicate unauthorized and potentially criminal actions, supported by 37 (Climbing wall) and 71 (Breaking door), suggesting forced entry or unauthorized access.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other1/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frame 51 shows an individual covering their face followed by a robbery in progress at frame 54. These actions indicate a potentially serious situation.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other1/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Multiple instances of theft and robbery actions detected at timestamps like 00:00:26, 00:00:39, 00:01:21, 00:04:09, among others, indicating a high level of suspicion.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other1/4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames 28, 34, 41, 52, 54, 59, 61, 75, 78, 102, 104, 115, 118, 194, 203, 205, 238, 247, 290, 300, 129, 318, 323, 335, 351, 389, 403, 434, 438, 456, 461, 468, 471, 473, 478, 490, 514, 519, 551, 554, 646, 709, 758, 756, 836, 843 indicate suspicious actions such as stealing, vandalism, and potential break-ins.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other1/5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Frames 6, 9, 52, 74, 90, 95, 129, 140 indicate multiple instances of stealing across different timestamps and locations.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other1/7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 00:00:05 to 00:00:16 and 00:01:37 to 00:01:47 indicate multiple explosions, fires, and smoke, suggesting a serious incident.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other1/8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected vandalism - breaking objects", "level_of_suspicion": 4, "reasoning": "Frame 143 shows smashing vase, indicating potential vandalism.", "class_number": 5}}, {"input": "/SOLUTION/Demo_videos/other1/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected robbery in progress", "level_of_suspicion": 5, "reasoning": "Multiple instances of robbery in progress detected across frames 0-2 and theft-related actions at frames 6 and 9 indicate an ongoing robbery activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other1/10.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Frames 00:00:04, 00:00:24, 00:00:50, and 00:01:03 show fighting and physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other1/11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Frames 23, 34, 59, 74 indicate stealing and robbery actions which are highly suspicious.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other1/12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Multiple actions related to explosives and fire detected in frames 4, 14, 35, 95, 135, 154.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other1/14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "The frames from 00:00:21 to 00:00:32 show running away from the camera, punching a man, theft through a window, and wall climbing which indicates potential burglary.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other1/16.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames 00:00:23 to 00:00:41 show fire and explosion in the building, along with fire alarms going off.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other1/18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 9, 44, 45, 48, 107, 127 indicate robbery actions like jewelry store robbery, electronics store robbery, and store robbing.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other1/19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Multiple frames show fire, smoke, and explosions indicating potential fire outbreak or explosion, starting around 00:00:19 with burning bed to 00:09:14 with fire hazard warning sign, with numerous instances of fire and smoke throughout.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other1/21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 34 show a man pointing a gun in a room, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other1/22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected explosion - fire, smoke, heat patterns, flying objects", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:15 to 00:02:14 indicate multiple explosion incidents and fire related actions.", "class_number": 6}}, {"input": "/SOLUTION/Demo_videos/other1/23.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Fighting detected in frames 27, 31, 55, 57, and 64 indicating repeated instances of physical altercation.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple frames indicating robbery and theft activities observed at timestamps: 00:00:20, 00:00:22, 00:00:28, 00:00:32, 00:00:34, 00:00:39, 00:00:41, 00:00:44, 00:01:16, 00:01:24, 00:01:25, 00:01:39, suggesting a significant robbery incident in progress.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of shoplifting and stealing are detected at timestamps 00:00:04, 00:00:06, 00:00:09, 00:00:24, 00:00:26, 00:00:28, 00:00:30, 00:00:31, 00:01:04, 00:01:13, 00:01:19, 00:01:34, 00:02:19, and 00:02:24.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions", "level_of_suspicion": 5, "reasoning": "Multiple frames include shoplifting, stealing, and robbery activities - specifically frames 6-9, 29, 37, 40-41, 44, 56, 59, 62, 63, 66, 73, 74, 80, 86, 90, 94, 99-100 indicate heightened suspicious activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/7.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Suspicious actions identified include theft at timestamp 00:00:24, robbery at timestamp 00:00:29, robbing store employee at timestamp 00:00:34, and stealing at timestamp 00:00:35.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/8.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 5, "reasoning": "Multiple instances of fighting detected at timestamps 00:00:09, 00:00:29, 00:00:59, 00:01:14, 00:01:24, 00:01:30, 00:01:59 indicating a severe physical altercation issue in the store.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:03 to 00:00:14 and 00:00:34 to 00:00:49 indicate actions related to theft, shoplifting, robbing cash register, and stealing, which are consistent with robbery behaviors.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/10.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Multiple instances of shoplifting and theft observed at timestamps 00:00:20, 00:00:29, 00:00:49, 00:00:54, 00:00:55, along with breaking objects at 00:00:59 and fighting at 00:01:04 and 00:01:09.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 4, "reasoning": "Frames 5, 36, 39, 46, 49, and 54 indicate theft and robbery activities including stealing, shoplifting, and robbery from a store and electronics store.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/13.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect physical altercation or fights or workplace violence", "level_of_suspicion": 4, "reasoning": "Suspicious activity detected at timestamp 00:00:02 with two people fighting.", "class_number": 1}}, {"input": "/SOLUTION/Demo_videos/other/14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Multiple instances of shoplifting and stealing observed at timestamps 00:00:02, 00:00:08, 00:00:17, 00:00:19, 00:00:21, 00:00:24, 00:00:26, 00:00:39, 00:00:45, 00:00:49, 00:01:25, 00:01:26, and 00:01:28.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/15.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 7 to 9 indicate robbing, stealing money, and grabbing a person\"s bag, combined with robbery storefront at frame 29 suggests a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/16.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 12 to 30 and 45 to 63 detect multiple thefts and potential robbery actions such as robbing cashier and shoplifting.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Multiple instances of shoplifting and stealing detected at timestamps 00:00:08, 00:00:24, 00:01:09, 00:02:50, and 00:03:52 indicate persistent suspicious activity.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/5.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames from 00:00:14 to 00:01:44 show stealing car keys, robbery, shoplifting, and robbing cash register, indicating suspicious behavior aligning with potential robbery.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 11 to 22 show a pattern of running, and frame 21 specifies a robbery, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/2.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 24 to 40 show theft and robbery activities in an electronics store.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/3.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Multiple instances of theft and shoplifting observed, such as breaking into gym and stealing equipment (00:00:04), theft in progress (00:00:06), robbery (00:00:20), various instances of shoplifting and stealing merchandise from shelves over durations 00:00:29 to 00:02:10.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/4.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary", "level_of_suspicion": 5, "reasoning": "Multiple frames indicate suspicious actions such as a person entering a van at night (00:00:14), stealing gasoline (00:02:09), man stealing gas (00:02:11), and theft from van (00:02:26).", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/6.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple instances of robbery detected at frames 6, 17, 18, 38, and 46 indicating a high level of criminal activity.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/9.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 5, "reasoning": "Repeated actions of shoplifting and stealing observed at frames 00:00:04, 00:00:21, 00:00:24, 00:00:30, 00:00:34, 00:00:55, 00:01:21, 00:01:40, 00:01:52, 00:02:00, 00:02:04, 00:02:09, 00:02:19, 00:02:21, 00:02:29 indicate highly suspicious activity.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/10.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Frames from timestamp 00:00:08 to 00:00:40 indicate suspicious activities such as shoplifting and stealing items.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/11.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Multiple frames show climbing walls, breaking windows, forcing doors, and acts of robbery from frames 2 to 193, indicating burglary in progress.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/12.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "The frames from 00:00:06 to 00:01:14 indicate actions such as robbing store, robbery electronics store, and use of a weapon which suggests a high likelihood of a robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/14.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Multiple frames (3, 6, 9, 19, 24) indicate actions related to stealing or theft in a store context.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/17.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 00:00:01 to 00:00:02 involve a robbery, which indicates a forced theft incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/18.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 4, 22, 29, 33, 34, 35, 39, 43, 51, 71, 94 show suspicious actions including shoplifting, robbery attempts, and car break-ins, indicating potential robbery incidents.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/19.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple instances of shoplifting and robbery detected in frames 9, 29, 34, 41, 55, 58, 59, 60, 63, 66, 69, 71, 72, 75, 79, 84, 86, 88, 89, 94, leading to a pattern of potential robbery incidents.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/20.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple instances of robbery and stealing are detected at timestamps 00:00:09, 00:00:22, 00:00:28, 00:00:41, 00:01:37, 00:01:59, 00:02:04, 00:02:17, 00:02:49, 00:03:04 indicating a high risk of robbery activities across different frames.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/21.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Multiple actions such as robbery, stealing, and fighting occur between timestamps 00:00:09 and 00:02:00, indicating a series of suspicious activities.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/22.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detect suspicious individual actions - shoplifting, stealing", "level_of_suspicion": 4, "reasoning": "Frames 21, 24, and 26 show actions related to stealing and shoplifting.", "class_number": 4}}, {"input": "/SOLUTION/Demo_videos/other/23.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Frames 19, 39, 53, 119, 139, 159 indicate theft, unauthorized access, and breaking objects.", "class_number": 7}}, {"input": "/SOLUTION/Demo_videos/other/24.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 7 to 11 show actions of robbing store, hiding face, and robbery, indicating a potential robbery incident.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/25.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 26 to 27 indicate a robbery occurring with evidence of stealing and fleeing the store.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/other/26.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected potential robbery incident", "level_of_suspicion": 5, "reasoning": "Frames 14, 24, 64, 68, and 79 indicate theft and armed robbery actions in a parking lot, associated with threatening behavior and stealing.", "class_number": 8}}, {"input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4", "output": {"is_suspicious": "Yes", "suspicious_activity": "Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In", "level_of_suspicion": 5, "reasoning": "Multiple instances of suspicious activities between timestamps 00:00:09 (Robbery car park), 00:01:08 (van being stolen), 00:01:44 (Man stealing truck) etc. indicate forced entry and theft in the parking lot."}}]