{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_1.mp4",
    "error": "Invalid JSON response"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_3.mp4",
    "error": "Invalid JSON response"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_6.mp4",
    "error": "Invalid JSON response"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_2.mp4",
    "error": "Invalid JSON response"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_1.mp4",
    "error": "Invalid JSON response"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_2.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  \"is_suspicious\": \"No\",\n  \"suspicious_activity\": \"\",\n  \"level_of_suspicion\": 1,\n  \"reasoning\": \"The only action detected is 'Accident' at timestamp 00:00:01, which does not align with any suspicious activities in the given list.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_13.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  \"is_suspicious\": \"Yes\",\n  \"suspicious_activity\": \"Detect suspicious individual actions - stealing\",\n  \"level_of_suspicion\": 5,\n  \"reasoning\": \"Frame 4 shows 'Stealing from vehicle parking lot', which is a highly suspicious activity.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_19.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  'is_suspicious': 'Yes',\n  'suspicious_activity': 'Detected potential robbery incident',\n  'level_of_suspicion': 5,\n  'reasoning': 'Frame 19 indicates drunk driving which can be associated with reckless or harmful behavior; frame 20 indicates a \"drive by,\" which in many contexts refers to a drive-by shooting, a common method in robberies or attacks.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_22.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frame 14 shows an action labeled \"Robbing gas station\", which indicates a robbery is taking place.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_24.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected burglary',\n 'level_of_suspicion': 4,\n 'reasoning': 'The action \"Climbing wall\" at timestamp 00:00:15 suggests potential trespassing, indicating unauthorized access.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_26.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected burglary - Trespassing, Forced Entry, Unauthorized Access\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"The action 'Climbing Wall' at timestamp 00:00:00 suggests unauthorized access, followed by multiple 'Explosion' actions at timestamps 00:00:05, 00:00:11, and 00:00:12, indicating serious suspicious activity.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/explosion/explosion_29.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected burglary, Detected potential robbery incident, Detect vandalism, Detected explosion',\n 'level_of_suspicion': 5,\n 'reasoning': 'Suspicious activities observed: \"Stealing car\" at timestamp 00:00:06, \"vandalism building\" at 00:00:09, \"Looting building\" at 00:00:22, \"Robbery street\" at 00:00:44, \"Explosion fire street\" at 00:00:54, \"Smoke bomb\" at 00:01:01, \"Fire\" at 00:01:02, and \"Smoke\" at 00:01:03 indicate heightened risk and potential criminal activity.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_7.mp4",
    "error": "Invalid JSON response",
    "response": "{ \n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detect suspicious individual actions - stealing', \n 'level_of_suspicion': 2, \n 'reasoning': 'The action \"shopping cart walking\" at timestamp 00:00:03 is unusual in a warehouse, which might indicate potential theft.', \n }"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_11.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detected burglary', \n 'level_of_suspicion': 3, \n 'reasoning': 'Frame at timestamp 00:00:06 indicates \"climbing wall\", which could suggest unauthorized access or attempted break-in.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_18.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detect suspicious individual actions - stealing',\n 'level_of_suspicion': 4,\n 'reasoning': 'Action \"Stealing items from store\" detected at timestamp 00:00:04.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_30.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected burglary',\n    'level_of_suspicion': 4,\n    'reasoning': 'Suspicious activity of \"Theft of machine\" detected at frame with timestamp 00:00:04.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/Fighting/Fighting_30.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect physical altercation or fights or workplace violence',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frames from timestamps 00:00:14 to 00:01:00 indicate multiple instances of physical altercations such as \"Fist fight\", \"Fighting person\", \"Punching person\", \"Punching people\", \"Fighting person patio\", and \"Fighting man\" which are clear signs of violent behavior.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_16.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  \"is_suspicious\": \"No\",\n  \"suspicious_activity\": \"\",\n  \"level_of_suspicion\": 1,\n  \"reasoning\": \"The action 'sitting at table' noted in frame 1 does not match any predefined suspicious activities.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_18.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detect potential health incidents - fainting/falling/firstaid', \n 'level_of_suspicion': 3, \n 'reasoning': 'The action \"Lying on ground\" at timestamp 00:00:06 could indicate a potential health incident.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_24.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected burglary - Theft with Structural Damage',\n 'level_of_suspicion': 5,\n 'reasoning': 'Frame at timestamp 00:00:02 shows \"Stealing from cash register\", indicating a theft.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/unattended_bag/temp_21.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Frame 129 shows 'Robbing man walking bench', and frame 289 shows 'Man running away with package', suggesting a robbery incident.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_11.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detect physical altercation or fights or workplace violence\",\n    \"level_of_suspicion\": 4,\n    \"reasoning\": \"Actions labeled as 'Fighting', 'Fistfight', and 'Fighting person' occur at timestamps 00:00:30, 00:00:36, 00:00:37, 00:00:39, and 00:00:50 indicating ongoing physical altercations.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_12.mp4",
    "error": "Invalid JSON response",
    "response": "{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect physical altercation or fights or workplace violence',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frame at timestamp 00:00:10 shows \"Fighting person\", and another incident at 00:03:19 reports \"Fighting person station\".'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_14.mp4",
    "error": "Invalid JSON response",
    "response": "{ \n    'is_suspicious': 'Yes', \n    'suspicious_activity': 'Detect suspicious individual actions - hiding baggage, unattended bags, stealing', \n    'level_of_suspicion': 4, \n    'reasoning': 'Suspicious activity detected at timestamp 00:00:19 involving \"Stealing bag classroom\". Presence of \"Open box bag classroom\" at timestamp 00:00:54 and \"Package on desk\" followed by \"Package left on desk\" without oversight suggests potential unattended bag or theft activity.',\n}"
}
{
    "input": "/SOLUTION/new_eval_data/unattended_bag/Unattended_bags_16.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detect vandalism - breaking objects',\n 'level_of_suspicion': 3,\n 'reasoning': 'Frame 24 indicates suspicious activity of \"painting graffiti wall\", which can be considered as vandalism.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/falling/falling_11.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected suspicious individual actions - stealing',\n 'level_of_suspicion': 5,\n 'reasoning': 'Frame at timestamp 00:00:09 shows the action \"Stealing purse counter\", which indicates a theft in progress.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/rob/robbery_4.mp4",
    "error": "Invalid JSON response",
    "response": "{ \n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detect physical altercation or fights or workplace violence', \n 'level_of_suspicion': 4, \n 'reasoning': 'Suspicious activity detected between timestamps 00:00:00 to 00:00:04 with actions including \"Fist fighting\", \"Fighting person\", and \"Hit man on building\".', \n }"
}
{
    "input": "/SOLUTION/new_eval_data/rob/Robbery_2.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident - weapon use\",\n    \"level_of_suspicion\": 4,\n    \"reasoning\": \"Shooting car at timestamps 00:00:07 and 00:01:12 indicates potential weapon use; 'Robbery car door' at 00:00:20 and 'Stealing gas' at 00:00:45 suggest a potential robbery incident.\"\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/rob/Robbery_8.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In',\n 'level_of_suspicion': 5,\n 'reasoning': 'Suspicious actions such as \"Trespassing parking lot\" at frame 24, \"Theft car park lot\" at frame 39, and \"Theft\" at frame 43 indicate burglary activities with high/red alert suspicion.'\n}"
}
{
    "input": "/SOLUTION/new_eval_data/rob/Robbery_16.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 4,\n    'reasoning': 'Actions such as \"Theft in progress\", \"Robbery\", \"Carjack\", \"Man pulling woman from car\", and \"Stealing car keys\" indicate a series of suspicious or criminal activity that points to a potential robbery incident. Frames 0, 9, 11, 12, 19, 22, 34, 43, 44 are suspicious.'\n}\n```"
}
{
    "input": "/SOLUTION/new_eval_data/rob/Robbery_36.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frames showing actions such as \"Shooting\", \"Stealing car\", \"Robbing\", and \"Man holding gun\" between timestamps 00:00:01 to 00:02:15 indicate high-risk criminal activities including weapon use and forced theft.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In',\n    'level_of_suspicion': 5,\n    'reasoning': 'Suspicious actions such as \"Break window\" at 00:00:41, \"Climbing fence\" at 00:00:42, \"Smash Window\" at 00:02:12, \"Breaking window\" at 00:02:14, \"Forced entry\" at 00:02:23, \"Theft jewelry store\" at 00:03:09, indicate a pattern of burglary.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/output_clip.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect physical altercation or fights or workplace violence',\n    'level_of_suspicion': 4,\n    'reasoning': 'Several instances of physical altercations including \"Fighting person\" and \"Punch in face man\" detected in frames 66, 75, 121, 123, 134, 144, 145, 146, and 153.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/11.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detect suspicious individual actions - shoplifting',\n 'level_of_suspicion': 5,\n 'reasoning': 'Multiple instances of \"Shoplifting\" detected at timestamps 00:00:01, 00:00:04, 00:00:39, 00:00:54, 00:01:29, indicating ongoing suspicious activity.',\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/13.mp4",
    "error": "Invalid JSON response",
    "response": "{ \n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detected potential robbery incident', \n 'level_of_suspicion': 5, \n 'reasoning': 'Actions detected include \"robbing store\" at 00:00:02, \"looting shelf\" at 00:00:10, \"shoplifting\" at 00:00:15 and 00:00:16, \"robbery\" at 00:00:18, \"running away\" at 00:00:20, \"theft in progress\" at 00:00:24, suggesting a cohesive sequence of a robbery incident.', \n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/20.mp4",
    "error": "Invalid JSON response",
    "response": "{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Multiple frames involved in robbery activities, including \"Robbery convenience store\" at 00:00:19, \"Robbery cashier counter\" at 00:00:44, \"Robbing store\" at 00:00:46, and \"Robbery man on bike\" at 00:00:49.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_18.mp4",
    "error": "Invalid JSON response",
    "response": "{ \n    'is_suspicious': 'Yes', \n    'suspicious_activity': 'Detected potential robbery incident', \n    'level_of_suspicion': 4, \n    'reasoning': 'Actions like \"Man grabbing cash register\" at timestamp 00:00:30 and \"pickpocketing bag person\" at timestamp 00:00:39 indicate a potential robbery incident.', \n}"
}
{
    "input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_5.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Frame 44 shows 'Soldier aiming at man' which may indicate weapon use and frame 49 shows 'Robbing person station', both indicating a potential robbery incident.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected burglary - Theft',\n    'level_of_suspicion': 5,\n    'reasoning': 'Actions labeled \"Stealing money counter\" at timestamp 00:01:04 and \"Stealing phone\" at timestamp 00:01:58 indicate theft activity.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/3.mp4",
    "error": "Invalid JSON response",
    "response": "{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect physical altercation or fights or workplace violence',\n    'level_of_suspicion': 4,\n    'reasoning': 'The action \"Punching man elevator\" at timestamp 00:00:44 indicates a physical altercation.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/5.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detect physical altercation or fights or workplace violence',\n 'level_of_suspicion': 4,\n 'reasoning': 'Frames 9 and 44 show \"Fighting person station\" and \"Man is pushing man\", indicating possible violent behavior.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/unattented_bags_18.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect vandalism - breaking objects',\n    'level_of_suspicion': 4,\n    'reasoning': 'The action \"punch mirror\" at timestamp 00:00:34 indicates potential vandalism.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/2.mp4",
    "error": "Invalid JSON response",
    "response": "{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing',\n    'level_of_suspicion': 5,\n    'reasoning': 'The actions of \"Stealing laptop\", \"Abandoning package\", \"Stalker hides suitcase station\", and multiple instances of \"Luggage abandoned\" between frames 1-349 suggest suspicious behavior involving theft and unattended/hidden bags.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/12.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Suspicious activity \"Robbing bank parking lot\" detected at timestamp 00:00:09'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/13.mp4",
    "error": "Invalid JSON response",
    "response": "{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frames 554 to 628 show multiple instances of stealing and robbery including \"Robbing cash register store\", \"Stealing food counter\", \"Man stealing computer monitor\", and \"Stealing computer\".'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/30.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  \"is_suspicious\": \"Yes\",\n  \"suspicious_activity\": \"Detect suspicious individual actions - shoplifting, stealing\",\n  \"level_of_suspicion\": 4,\n  \"reasoning\": \"The action 'Theft carton street' at timestamp 00:00:09 indicates a stealing incident.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/unattented_bags/output_clip.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detected potential robbery incident', \n 'level_of_suspicion': 5, \n 'reasoning': 'Frame 4 shows \"Robbing bank bike\" followed by \"Runner fleeing scene\" in frame 9 indicating a robbery attempt.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/unattented_bags/unattented_bags_1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  'is_suspicious': 'Yes',\n  'suspicious_activity': 'Detect suspicious individual actions - unattended bags, stealing',\n  'level_of_suspicion': 4,\n  'reasoning': 'Frame 24 indicates \"Stealing luggage from bench <station>\" and frame 49 indicates \"Suitcase unattended platform\". Both actions are examples of suspicious individual actions and suggest potential theft.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other1/1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected burglary',\n    'level_of_suspicion': 4,\n    'reasoning': 'Frames 35 and 46 indicate \"Stealing vending machine\" and \"Theft\", showing unauthorized access and stealing activity inside a building.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other1/15.mp4",
    "error": "Invalid JSON response",
    "response": "{\n  'is_suspicious': 'Yes',\n  'suspicious_activity': 'Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In',\n  'level_of_suspicion': 5,\n  'reasoning': 'Frame 19 shows suspicious activity in a room followed by frame 21 which indicates a \"Breaking door\" action, indicative of a forced entry.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detect suspicious individual actions - hiding baggage, unattended bags, shoplifting, stealing\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Multiple instances of 'Theft', 'Shoplifting', and 'Stealing' detected in frames 3, 7, 19, 20, 24, 26, 29, 30, 40, 41, 49, 59, 69, 74, 79, 92, 94, 129 indicating high levels of suspicious activity.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/5.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Frames 9-21 and 29-34 indicate actions consistent with robbery activities such as 'Robbery cash register', 'Shove someone counter', and 'Running from camera'.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/5.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 4,\n    \"reasoning\": \"The actions 'Robbing man' at 00:00:00 and 'Running from police' at 00:01:26 indicate a robbery situation, combined with 'Theft', 'Stealing item shelf', and multiple 'Shoplifting' actions suggests high suspicion level.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/7.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Suspicious activities such as 'Robbery', 'Theft cash register', 'Shoplifting', and 'Robbing store' were detected in multiple frames (28, 29, 47, 49, 50, 59, 76, 92, 97, 99, 119).\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/8.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected potential robbery incident',\n 'level_of_suspicion': 5,\n 'reasoning': 'Frames 5-10 involve \"fighting\" and \"theft\", frame 19 involves \"robbing car door\", frames 58-59 involve \"shooting gun\" and \"theft of backpack\".'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/13.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  'is_suspicious': 'Yes',\n  'suspicious_activity': 'Detected potential robbery incident',\n  'level_of_suspicion': 5,\n  'reasoning': 'The action \"Robbery grocery store counter\" at timestamp 00:00:04 indicates a robbery incident, which is a serious crime.'\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/other/15.mp4",
    "error": "Invalid JSON response",
    "response": "{\n  \"is_suspicious\": \"Yes\",\n  \"suspicious_activity\": \"Detected potential robbery incident\",\n  \"level_of_suspicion\": 5,\n  \"reasoning\": \"Multiple robbery actions detected including 'Robbery checkout counter' at frame 19, 'Robbing gas station' at frame 24, 'Robbery store front' at frame 39, 'Robbing cash register' at frame 101, and additional suspicious actions like 'Breaking door' at frame 70 and 'Stealing cigarettes aisle' at frame 109.\"\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/16.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes', \n 'suspicious_activity': 'Detected burglary - Trespassing, Forced Entry, Unauthorized Access, Theft with Structural Damage, Attempted Break-In', \n 'level_of_suspicion': 4, \n 'reasoning': 'Suspicious activity observed at frame 4 with \"Stealing car door\", indicating potential burglary in progress in parking lot area.'\n}"
}
{
    "input": "/SOLUTION/Demo_videos/other/27.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detect suspicious individual actions - shoplifting, stealing\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Frames 19-81 indicate multiple instances of theft including 'Theft of product', 'Shoplifting checkout', 'Stealing cigarettes shelf', 'Stealing', 'Shoplifting', and 'Stealing carton milk shelf'.\"\n}\n```"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/",
    "error": "list index out of range"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "run_pipeline() missing 1 required positional argument: 'output_path'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "image must be bytes, path-like object, or file-like object"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "image must be bytes, path-like object, or file-like object"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "name 'cv2' is not defined"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "'constructor'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "'constructor'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "image must be bytes, path-like object, or file-like object"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "cannot unpack non-iterable NoneType object"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "name 'load_yaml' is not defined"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "name 'load_yaml' is not defined"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "run_pipeline() missing 1 required positional argument: 'output_path'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_1.mp4",
    "error": "name 'load_yaml' is not defined"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/",
    "error": "run_pipeline() got an unexpected keyword argument 'output_path'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/",
    "error": "list index out of range"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/",
    "error": "list index out of range"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_3.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'No',\n    'suspicious_activity': 'None',\n    'level_of_suspicion': 1,\n    'reasoning': 'All timestamps show \"No Action\", indicating no suspicious behavior detected.'\n}\n```"
}
{
    "input": "Segment 0",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 1",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 2",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 3",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 4",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 5",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 6",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 7",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 8",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 9",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 10",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 11",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 12",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 13",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 14",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 15",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 16",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 17",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 18",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 0",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 1",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 2",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 3",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 4",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 5",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 6",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 7",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 8",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 9",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 10",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 11",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 12",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 13",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 14",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 15",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 16",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 17",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 18",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 0",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 1",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 2",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 3",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 4",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 5",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "Segment 6",
    "error": "run_pipeline() got an unexpected keyword argument 'frames'"
}
{
    "input": "/SOLUTION/Demo_videos/vandalism/vandalism_3.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Suspicious activities identified include 'Robbing store' at timestamp 00:00:04, 'Robbery car burglary' at 00:00:19, and 'Robbing car' at 00:03:45. These actions strongly indicate robbery incidents.\"\n}\n```"
}
{
    "input": "/SOLUTION/results/analysis_video/output_video.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/results/analysis_video/output_video.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/results/analysis_video/output_video.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/results/analysis_video/output_video.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'output_path'"
}
{
    "input": "/SOLUTION/results/analysis_video/output_video.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'output_path'"
}
{
    "input": "/SOLUTION/CES_demo_data/unstiched_video_analysis/",
    "error": "cannot unpack non-iterable NoneType object"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/burglary_1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'The sequence of actions between frames 49 and 194 indicate multiple robbery attempts and actual robberies, including \"Robbery parking lot\" at 00:00:49 and \"Robbing jewelry store\" at 00:03:14.'\n}\n```"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/burglary_6.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frames 31 (Robbing vehicle), 39 (Theft of vehicle), 49 (Robbery Parking Lot), 53 (Vandalism), 94 (\"Robbery parking lot.\"), 104 (Robbery car theft), 114 (Driving van parked street), 144 (Robbery van parking lot) indicate a pattern of robbery and theft, especially in a parking lot context, suggesting a coordinated robbery incident.'\n}\n```"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/fighting_3.mp4",
    "error": "Invalid JSON response",
    "response": "{\n 'is_suspicious': 'Yes',\n 'suspicious_activity': 'Detected potential robbery incident',\n 'level_of_suspicion': 5,\n 'reasoning': 'Multiple actions indicating robbery such as \"Robbing store\", \"Armed robbery store counter\", \"Robbery\", and \"Robbery grocery store\" in frames from 00:00:08 to 00:02:14.'\n}"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/fighting_6.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    \"is_suspicious\": \"Yes\",\n    \"suspicious_activity\": \"Detected potential robbery incident\",\n    \"level_of_suspicion\": 5,\n    \"reasoning\": \"Multiple instances of robbery actions observed between timestamps 00:00:09 to 00:02:04 including 'Robbery suspect aisle', 'Robbery cash register', 'Robbing store', and 'Smash and grab store'.\"\n}\n```"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/shoplifting_4.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n  \"is_suspicious\": \"Yes\",\n  \"suspicious_activity\": \"Detected potential robbery incident\",\n  \"level_of_suspicion\": 5,\n  \"reasoning\": \"Frames between 00:00:04 and 00:00:14 indicate robbery actions including 'Robbery convenience store' and 'Robbery cashier customer'.\"\n}\n```"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/shoplifting_8.mp4",
    "error": "Invalid JSON response",
    "response": "{\n  'is_suspicious': 'Yes',\n  'suspicious_activity': 'Detected potential robbery incident',\n  'level_of_suspicion': 5,\n  'reasoning': 'Suspicious activities such as \"Robbing store\", \"Robbery convenience store\", \"Theft\", \"Robbery cash register\", \"Robbery pharmacy aisle\", \"Man steals fish\" observed in frames 20, 23, 34, 36, 44, 54, 64, 83, 89, 108, 129, 239.'\n}"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/unstiched_video_analysis/unattented_bags_1.mp4",
    "error": "Invalid JSON response",
    "response": "```json\n{\n    'is_suspicious': 'Yes',\n    'suspicious_activity': 'Detected potential robbery incident',\n    'level_of_suspicion': 5,\n    'reasoning': 'Frame at 00:01:34 shows \"Man stealing suitcase station\" and frame at 00:01:61 indicates \"Man with gun\", suggesting a robbery incident.'\n}\n```"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_1_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_2_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_3_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_4_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_5_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_6_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_7_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_8_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_9_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_10_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_11_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_12_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_13_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_14_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_15_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/ces_demo/CES_demo_data/streams/final_streams/stream_16_videos.mp4",
    "error": "run_pipeline() got an unexpected keyword argument 'chunk_duration'"
}
{
    "input": "/SOLUTION/demo/test/robbery_7_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_3_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_4_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/robbery_1_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/burglery_5_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/vandalism_1_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/vandalism_3_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/fighting_5_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/burglery_7_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/falling_1_shortened.mp4",
    "error": "module 'src.pipelines.pipeline_video_basic_lean' has no attribute 'run_pipeline'"
}
{
    "input": "/SOLUTION/demo/test/robbery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_4_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/robbery_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/fighting_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/falling_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/robbery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_4_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/robbery_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/fighting_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/falling_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/robbery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/shoplifting_4_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/robbery_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/vandalism_3_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/fighting_5_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/burglery_7_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/demo/test/falling_1_shortened.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "\n\nYou tried to access openai.Embedding, but this is no longer supported in openai>=1.0.0 - see the README at https://github.com/openai/openai-python for the API.\n\nYou can run `openai migrate` to automatically upgrade your codebase to use the 1.0.0 interface. \n\nAlternatively, you can pin your installation to the old version, e.g. `pip install openai==0.28`\n\nA detailed migration guide is available here: https://github.com/openai/openai-python/discussions/742\n"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_1.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_2.f401.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_3.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_4.f136.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_5.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_6.f625.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_7.f614.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_9.f135.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/use_cases_video/security_breach/security_breach_10.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract methods execute, load_model"
}
{
    "input": "/SOLUTION/ces_demo/10.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method load_model"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "DeconstructorLean.__init__() takes 3 positional arguments but 6 were given"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "DeconstructorLean.__init__() takes 3 positional arguments but 6 were given"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_1.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_2.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_4.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_5.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_6.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_7.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_1.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_2.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_4.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_5.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_6.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
    "input": "/SOLUTION/standard_dataset/burglary/burglary_7.mp4",
    "error": "'DeconstructorLean' object has no attribute 'embedding_model_name'"
}
{
<<<<<<< HEAD
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "empty vocabulary; perhaps the documents only contain stop words"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/security_breach/security_breach_1.f134.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
=======
    "input": "/SOLUTION/standard_dataset/customer_behaviour/customer_behaviour_1.f136.mp4",
    "error": "'DeconstructorLean' object has no attribute 'constructor_actions'"
}
{
    "input": "/SOLUTION/standard_dataset/customer_behaviour/customer_behaviour_1.f136.mp4",
    "error": "'DeconstructorLean' object has no attribute 'action_patterns'"
}
{
    "input": "/SOLUTION/standard_dataset/shoplifting/shoplifting_1.mp4",
    "error": "'actions'"
}
{
    "input": "/SOLUTION/standard_dataset/shoplifting/shoplifting_1.mp4",
    "error": "'actions'"
}
{
    "input": "/SOLUTION/standard_dataset/shoplifting/shoplifting_1.mp4",
    "error": "Can't instantiate abstract class DeconstructorLean with abstract method execute"
>>>>>>> 7b2cd1da2f90797246dcac1370ada7774cb15e9f
}
